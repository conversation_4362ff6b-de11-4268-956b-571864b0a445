trigger:
- main

stages:
  - stage: CI
    pool:
      vmImage: ubuntu-latest
    jobs: 
    - job: CI
      variables:
        buildConfiguration: 'Release'
      steps:
      - task: UseDotNet@2
        displayName: 'Get .NET 9'
        inputs:
            version: 9.0.x
      - task: DotNetCoreCLI@2
        displayName: Nuget Restore
        inputs:
          command: 'restore'
          projects: 'PracticeDirectory.Client/PracticeDirectory.Client.csproj'
          feedsToUse: 'select'
      - task: DotNetCoreCLI@2
        displayName: Build
        inputs:
          command: 'build'
          projects: 'PracticeDirectory.Client/PracticeDirectory.Client.csproj'
          configuration: '$(BuildConfiguration)'
          arguments: '--no-restore'
      - task: DotNetCoreCLI@2
        displayName: Test
        inputs:
          command: 'test'
      - task: DotNet<PERSON>oreCLI@2
        displayName: Publish
        inputs:
          command: 'publish'
          publishWebProjects: true
          zipAfterPublish: true
          arguments: '--output $(Build.ArtifactStagingDirectory)'
      - publish: $(Build.ArtifactStagingDirectory)
        artifact: PracticeDirectoryClient
  - stage: Staging
    pool:
      name: Local Dev Servers
      demands:
        - agent.name -equals DEV-MYD-WEBNEW
    jobs: 
    - job: Staging
      steps:
      - download: current
        artifact: PracticeDirectoryClient 
      - task: IISWebAppDeploymentOnMachineGroup@0
        inputs:
          WebSiteName: 'Default Web Site'
          VirtualApplication: 'PracticeDirectory.Client.V1.DEV'
          Package: '$(Agent.WorkFolder)/3/PracticeDirectoryClient/*.zip'
          TakeAppOfflineFlag: true
  - stage: Production
    dependsOn:
    - CI
    - Staging
    pool:
      name: Local Prod Servers
      demands:
        - agent.name -equals MYD-WEB-NEW
    jobs: 
    - job: Production
      steps:
      - download: current
        artifact: PracticeDirectoryClient 
      - task: IISWebAppDeploymentOnMachineGroup@0
        inputs:
          WebSiteName: 'SSL Web Site'
          VirtualApplication: 'PracticeDirectory'
          Package: '$(Agent.WorkFolder)/2/PracticeDirectoryClient/*.zip'
          TakeAppOfflineFlag: true