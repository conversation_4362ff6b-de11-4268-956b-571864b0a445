﻿@page "/aboutus/editaboutus/{PracticeId:int}"
@using Microsoft.AspNetCore.Components.Authorization
@using PracticeDirectory.Client.Shared.Configuration
@using PracticeDirectory.Client.Shared.Extensions
@using PracticeDirectory.Client.Shared.Models
@using PracticeDirectory.Client.Service
@using PracticeDirectory.Client.Shared.Models.Practices
@inject IHttpClientFactory ClientFactory
@inject PracticeContextService PracticeContext
@inject NavigationManager Navigation
@inject UserContextService UserContext
@inject IJSRuntime JSRuntime

<h3>Practice About Us</h3>

<div class="text-end mb-2">
    <a href="practice/@PracticeId" class="btn btn-secondary" style="max-width: 240px; line-height: 30px;">
        Back To Practice
    </a>
</div>

@if (loading)
{
    <p>Loading...</p>
}
else if (loadError)
{
    <div class="alert alert-danger">Error loading data. Please try again later.</div>
}
else
{
    <EditForm Model="Practice" OnValidSubmit="SaveAboutUsAsync">

        @if (!string.IsNullOrWhiteSpace(errorMessage))
        {
            <div class="alert alert-danger">@errorMessage</div>
        }

        @if (!string.IsNullOrEmpty(Practice.draftAboutUs))
        {
            <InputTextArea class="form-control mb-3"
            @bind-Value="Practice.draftAboutUs"
            rows="8" />
        }
        else
        {
            <InputTextArea class="form-control mb-3"
            @bind-Value="Practice.aboutUs"
            rows="8" />
        }
        <div class="d-flex gap-2">
            @if (Practice.status != 1)
            {
                <button type="submit" class="btn btn-primary">Save</button>
            }
            <button type="button" class="btn btn-secondary" @onclick="GoBack">Cancel</button>

            @if (Practice.status == 0)
            {
                <HxButton Text="Submit for Approval" Color="ThemeColor.Primary" @onclick="SubmitforApproval" class="btn btn-primary" />
            }
        </div>
    </EditForm>

    
}

@code {
    [Parameter]
    public int PracticeId { get; set; }

    [CascadingParameter]
    private Task<AuthenticationState> authenticationStateTask { get; set; } = default!;

    private string userName = string.Empty;
    private PracticeAboutUs Practice = new();
    private bool loading = true;
    private bool loadError = false;
    private string errorMessage = string.Empty;

    private const int MinWordCount = 150;
    private const int MaxWordCount = 200;

    protected override async Task OnInitializedAsync()
    {
        var authState = await authenticationStateTask;
        userName = authState.GetWindowsUserName(UserContext.OverrideUsername);

        if (string.IsNullOrWhiteSpace(UserContext.OverrideUsername))
        {
            var storedOverride = await JSRuntime.InvokeAsync<string>("localStorage.getItem", "overrideUsername");
            if (!string.IsNullOrWhiteSpace(storedOverride))
            {
                UserContext.OverrideUsername = storedOverride;
            }
        }

        await LoadPracticeAsync();
    }


    private int CountWords(string text)
    {
        if (string.IsNullOrWhiteSpace(text)) return 0;
        return text.Split(new[] { ' ', '\n', '\r', '\t' }, StringSplitOptions.RemoveEmptyEntries).Length;
    }


    private async Task SubmitforApproval()
    {
        var wordCount = CountWords(Practice.draftAboutUs);
        if (wordCount > MaxWordCount)
        {
            errorMessage = $"Bio exceeds the {MaxWordCount}-word limit. Current count: {wordCount}. Please shorten it before submitting.";
            StateHasChanged();
            return;
        }
        else if (wordCount < MinWordCount)
        {
            errorMessage = $"Bio should be a minimum of {MinWordCount} words. Current count: {wordCount}.";
            StateHasChanged();
            return;
        }
        else
        {
            errorMessage = string.Empty;

            var httpClient = CreateAuthorizedClient();

            if (PracticeId == 0)
            {
                PracticeId = await getPracticeIdFromLocalStorage();
            }

            Practice.practiceId = PracticeId;
            Practice.status = 1;

            using HttpResponseMessage response = await httpClient.PutAsJsonAsync($"practiceaboutus/{PracticeId}", Practice);

            if (response.IsSuccessStatusCode)
            {
                Navigation.NavigateTo($"practice/{PracticeId}");
            }
            else
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                await JSRuntime.InvokeVoidAsync("alert", $"Failed to save changes. Server says: {responseContent}");
            }

            await LoadPracticeAsync();
        }


    }

    private async Task<int> getPracticeIdFromLocalStorage()
    {
        string storedPracticeId = await JSRuntime.InvokeAsync<string>("localStorage.getItem", "practiceId");
        if (!int.TryParse(storedPracticeId, out int practiceId))
            return 0;
        return practiceId;
    }

    private HttpClient CreateAuthorizedClient()
    {
        var httpClient = ClientFactory.CreateClient(ApiSettings.Api);
        httpClient.DefaultRequestHeaders.Clear();
        httpClient.DefaultRequestHeaders.Add("X-ORIG-NAME", userName);
        return httpClient;
    }

    private async Task LoadPracticeAsync()
    {
        try
        {
            var httpClient = CreateAuthorizedClient();
            var response = await httpClient.GetFromJsonAsync<PracticeAboutUs>($"practiceaboutus/{PracticeId}");
            Practice = response ?? new();          
        }
        catch
        {
            loadError = true;
        }
        finally
        {
            loading = false;
        }
    }

    private async Task SaveAboutUsAsync()
    {
        try
        {
            var httpClient = CreateAuthorizedClient();
            Practice.status = 0;

            if (string.IsNullOrWhiteSpace(Practice.draftAboutUs) && Practice.aboutUs != null)
            {
                Practice.draftAboutUs = Practice.aboutUs;
            }

            using HttpResponseMessage response = await httpClient.PutAsJsonAsync($"practiceaboutus/{PracticeId}", Practice);
            response.EnsureSuccessStatusCode();          

            if (response.IsSuccessStatusCode)
            {
                Navigation.NavigateTo($"aboutus/editaboutus/{PracticeId}");
               await LoadPracticeAsync();
            }
            else
            {
                await JSRuntime.InvokeVoidAsync("alert", "Failed to save changes.");
            }
        }
        catch
        {
            await JSRuntime.InvokeVoidAsync("alert", "An unexpected error occurred.");
        }
    }

    private void GoBack()
    {
        Navigation.NavigateTo($"practice/{PracticeId}");
    }
}
