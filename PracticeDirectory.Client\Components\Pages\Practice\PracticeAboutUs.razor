﻿@using Microsoft.AspNetCore.Components.Authorization
@using PracticeDirectory.Client.Shared.Configuration
@using PracticeDirectory.Client.Shared.Extensions
@using PracticeDirectory.Client.Shared.Models
@using PracticeDirectory.Client.Service
@using PracticeDirectory.Client.Shared.Models.Practices
@inject IHttpClientFactory ClientFactory
@inject UserContextService UserContext
@inject PracticeContextService PracticeContext
@inject IJSRuntime JSRuntime
@inject NavigationManager Navigation

<div class="row align-items-center mb-5">
    <div class="col-md-5 col-sm-12 mb-4 mb-md-0">
        <object data="@Practice.ImageUrl" type="image/jpeg">
            <img src="https://www.mydentist.co.uk//images/default-source/practices/practice-images/320x180/holding-image.jpg?v=4"
                 alt="Practice Image" class="img-fluid rounded-lg shadow-lg" />
        </object>
    </div>
    <div class="col-md-7 col-sm-12 d-flex align-items-center">
        <div class="text-muted" style="font-size: 1.125rem; line-height: 1.7; color: #555; padding-left: 30px;">
            @PracticeAbout.aboutUs

            @if (PracticeAbout.status == 1)
            {
                <div class="mt-3 text-warning font-weight-bold">
                    Status: Pending Approval
                </div>
            }
        </div>
    </div>

    @if (AddMode)
    {
        <div class="d-flex flex-column gap-1 mt-3">
            <button class="btn btn-primary" style="max-width: 240px;" @onclick="@(() => Navigation.NavigateTo($"aboutus/editaboutus/{Practice.Id}"))">
                Edit About Us
            </button>
        </div>
    }
</div>

@if (loadError)
{
    <div class="alert alert-danger" role="alert">
        Could not load content
    </div>
}

@code {
    [Parameter]
    public DirectoryPracticeModel Practice { get; set; } = new();

    [CascadingParameter]
    private Task<AuthenticationState> authenticationStateTask { get; set; } = default!;

    private string userName = string.Empty;
    private string isAuthenticated = string.Empty;
    private int PracticeId;
    private int CurrentUserPracticeId;
    private bool AddMode = false;
    private PracticeDirectory.Client.Shared.Models.Practices.PracticeAboutUs PracticeAbout = new();
    private bool loadError = false;

    protected override async Task OnInitializedAsync()
    {
        await LoadOverrideUsernameAsync();

        var authState = await authenticationStateTask;
        userName = authState.GetWindowsUserName(UserContext.OverrideUsername);
        isAuthenticated = authState.GetWindowsUserIsAuthenticated().ToYesNo();

        CurrentUserPracticeId = authState.GetPracticeId(ClientFactory, UserContext.OverrideUsername);
    }

    protected override async Task OnParametersSetAsync()
    {
        PracticeId = Practice.Id;
        await LoadOverrideUsernameAsync();

        var authState = await authenticationStateTask;
        userName = authState.GetWindowsUserName(UserContext.OverrideUsername);
        isAuthenticated = authState.GetWindowsUserIsAuthenticated().ToYesNo();

        await JSRuntime.InvokeVoidAsync("localStorage.setItem", "practiceId", Practice.Id);
        PracticeContext.PracticeId = Practice.Id;

        bool isCurrentManager = await GetCurrentPMAsync(Practice.Id, CurrentUserPracticeId);
        AddMode = isCurrentManager;

        await GetAboutusAsync();
    }

    private async Task LoadOverrideUsernameAsync()
    {
        if (string.IsNullOrWhiteSpace(UserContext.OverrideUsername))
        {
            var storedOverride = await JSRuntime.InvokeAsync<string>("localStorage.getItem", "overrideUsername");
            if (!string.IsNullOrWhiteSpace(storedOverride))
            {
                UserContext.OverrideUsername = storedOverride;
            }
        }
    }

    private async Task<bool> GetCurrentPMAsync(int parentPractice, int currentPractice)
    {
        return await Task.FromResult(parentPractice != 0 && currentPractice != 0 && parentPractice == currentPractice);
    }

    private async Task GetAboutusAsync()
    {
        try
        {
            var httpClient = ClientFactory.CreateClient(ApiSettings.Api);
            httpClient.DefaultRequestHeaders.Add("X-ORIG-NAME", userName);

            var response = await httpClient.GetFromJsonAsync<PracticeDirectory.Client.Shared.Models.Practices.PracticeAboutUs>($"practiceaboutus/{PracticeId}");
            PracticeAbout = response ?? new();
        }
        catch
        {
            loadError = true;
        }
    }
}
