{"Api": {"BackendUrl": "http://dev-myd-webnew/PracticeDirectory.Api.V1.Dev/"}, "GDCApi": {"GDCApiUrl": "http://myd-web-new/Person.API.v2/Clinician/CheckGdcRegistration"}, "FileStorageHub": {"URL": "https://apinew.mydentist.co.uk/FileStorageHub.Api.V2/api", "APIKey": "6OuOvkdfgOtBGlq75VhLEch5zfA5dV1IJmPm", "ImageUploadEndpointPath": "/Image/upload", "ImageDownloadEndpointPath": "/Image/download"}, "CommissioningTeamUsers": {"CommissioningTeamUserNames": "lnoi|hcranston|mark.price|vtoner|cpage|wmeakin|richard.lee|atandon|gberesford|cprathapan|ahemingway|efuchs|mhosni|pmathew"}, "MarketingTeamUsers": {"MarketingTeamUserNames": "mhosni|e<PERSON><PERSON>|ah<PERSON>ing<PERSON>|cprathapan|gberesford|atandon|richard.lee|lwaring|sha<PERSON><PERSON>|pmathew"}, "AdminTeamUsers": {"AdminTeamUsersNames": "mhosni|e<PERSON><PERSON>|ah<PERSON><PERSON><PERSON>|cpratha<PERSON>|atandon|richard.lee|pmathew"}, "PhoneNumberUsers": {"UserNames": "richard.lee|jclift|Cprathapan"}, "Impersonation": {"Username": "Bury-PM", "Impersonate": "No"}, "CustomAttributesSettings": {"CanEditeAttributes": "mhosni|e<PERSON><PERSON>|ah<PERSON>ing<PERSON>|cprathapan|atandon|richard.lee", "AttributeGroupSettings": {"General Information": ["FriendlyName", "ShortName", "RenovationText", "ReportingCountry"], "Patient Acceptance & Plans": ["AcceptingNewPatients", "AcceptingNHSPatients", "AcceptingPrivatePatients", "OffersNHSTreatments", "IsAffordablePrivate", "IsMyDentistPlan", "IsHygienePlan"], "Clinical Services & Specialisms": ["IsImplantCentre", "IsSedation", "IsShortTermOrtho", "HighLevelOrthodontics", "MySmileTeethStraightening", "OffersMySkinTreatments", "VirtualConsultation", "IsEmergencyPractice"], "Consultations & Booking": ["IsOfferingFreeConsultations", "OnlineBookingEnabled", "OLBLink", "FreeEmergencyUrl", "PaidEmergencyUrl"], "Facilities & Access": ["DisabledAccess", "DisabledParking", "Parking", "Wifi", "MeetingRooms", "LessPaper"], "Location": ["Latitude", "Longitude"], "System IDs & Codes": ["CQCLocationId", "GoogleBusinessId", "LegacyPracticeId", "ODSCode", "NHSAreaTeam"], "Media & URLs": ["3dVideoURL", "SmileViewURL", "DentalPlanAPublicURL", "DentalPlanBPublicURL", "DentalPlanCPublicURL", "DentalPlanChildrenPublicURL", "DentalPlanPublicURL"], "Flags & Trials": ["BespokeReferralForm", "FixedNumberTrial", "HigherTreatmentPricing", "Polyclinic", "IsStudentPractice"]}}, "DetailedErrors": true, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*"}