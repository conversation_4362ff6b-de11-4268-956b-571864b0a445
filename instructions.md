# Lab Invoice Functionality Extraction

This document provides instructions for extracting the lab invoice functionality from the PracticeView project and implementing it in a new project.

## 1. Project Setup

### 1.1. Create a new ASP.NET MVC Project

Create a new ASP.NET MVC project in Visual Studio.

### 1.2. NuGet Packages

Install the following NuGet packages:

*   Kendo.Mvc
*   Newtonsoft.Json
*   Microsoft.AspNet.WebApi
*   Microsoft.AspNet.Mvc
*   Microsoft.AspNet.Web.Optimization
*   jQuery
*   Knockout.js
*   Moment.js
*   jQuery.Validation
*   Modernizr
*   bootstrap

### 1.3. Database Connection

The application requires connections to two databases: `WarehouseNavision` and `Navision`. Add the following connection strings to your `Web.config` file, replacing the placeholders with your actual server and database names:

```xml
<connectionStrings>
  <add name="WarehouseNavision" connectionString="Data Source=YOUR_SERVER;Initial Catalog=WarehouseNavision;Integrated Security=True" providerName="System.Data.SqlClient" />
  <add name="Navision" connectionString="Data Source=YOUR_SERVER;Initial Catalog=Navision;Integrated Security=True" providerName="System.Data.SqlClient" />
</connectionStrings>
```

## 2. Backend

### 2.1. Models

Create the following C# model classes:

#### 2.1.1. `LabInvoiceModel.cs`

```csharp
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Linq;
using System.Runtime.Serialization;
using IDHGroup.SharedLibraries.DataAccessLayer;
using IDHGroup.SharedLibraries.Logging;
using IDHGroup.SharedLibraries.WisdomUsers;
using IDHGroup.UI.PracticeView.Enums;
using IDHGroup.UI.PracticeView.Extensions;
using IDHGroup.UI.PracticeView.Helpers;
using IDHGroup.UI.PracticeView.Helpers.Extensions;

namespace IDHGroup.UI.PracticeView.Models
{
    [DataContract]
    public class LabInvoiceModel
    {
        private readonly IDictionary<InvoiceOnHoldStatus, string> StatusToDbValue =
            new Dictionary<InvoiceOnHoldStatus, string>
            {
                {InvoiceOnHoldStatus.NotOnHold, ""},
                {InvoiceOnHoldStatus.Queried, "Y QUERY"}
            };

        public LabInvoiceModel(DataRow dataRow)
        {
            string onHoldValue = dataRow.GetColumnValueOrDefault<string>("On Hold");

            InvoiceNumber = dataRow.GetColumnValueOrDefault<string>("Invoice Number");
            PINumber = dataRow.GetColumnValueOrDefault<string>("PINumber");
            LabCode = dataRow.GetColumnValueOrDefault<string>("Lab Code");
            LabName = dataRow.GetColumnValueOrDefault<string>("Lab Name");
            WorkType = dataRow.GetColumnValueOrDefault<string>("Work Type");
            PracticeNumber = dataRow.GetColumnValueOrDefault<int>("Practice ID");
            Performer = dataRow.GetColumnValueOrDefault<string>("Performer");
            PostingDate = dataRow.GetColumnValueOrDefault<DateTime>("Posting Date");
            OnHold = dataRow.GetColumnValueOrDefault<string>("On Hold");
            LedgerEntryId = dataRow.GetColumnValueOrDefault<int>("Ledger Entry Id");
            DocumentDateTime = dataRow.GetColumnValueOrDefault<DateTime>("Document Date");
            LineType = dataRow.GetColumnValueOrDefault<string>("LineType");
            Amount = dataRow.GetColumnValueOrDefault<decimal>("Amount");

            IsInQuery = onHoldValue.ToLower() == StatusToDbValue[InvoiceOnHoldStatus.NotOnHold];

            // If the invoice represents credit then the amount should be negative.
            if (LineType.ToLower() == "credit note")
            {
                Amount *= -1;
            }
        }

        [DataMember]
        public decimal Amount { get; private set; }

        [DataMember]
        public DateTime DocumentDateTime { get; private set; }

        [DataMember]
        public string InvoiceNumber { get; private set; }

        [DataMember]
        public bool IsInQuery { get; private set; }

        [DataMember]
        public string LabCode { get; private set; }

        [DataMember]
        public string LabName { get; private set; }

        [DataMember]
        public int LedgerEntryId { get; private set; }

        [DataMember]
        public string LineType { get; private set; }

        [DataMember]
        public string OnHold { get; private set; }

        [DataMember]
        public string Performer { get; private set; }

        [DataMember]
        public string PINumber { get; private set; }

        [DataMember]
        public DateTime PostingDate { get; private set; }

        [DataMember]
        public int PracticeNumber { get; private set; }

        [DataMember]
        public bool Selected { get; private set; }

        [DataMember]
        public string WorkType { get; private set; }

        public static List<LabInvoiceModel> GetAllUnapprovedInvoices(int practiceNumber)
        {
            DataTable dataTable;
            const string sql =
                "SELECT * FROM View_NavisionUnauthorisedInvoices WHERE [Practice ID] = @practiceNumber ORDER BY [Document Date]";

            using (DAL dal = new DAL("WarehouseNavision"))
            {
                dataTable = dal.GetDataTable(sql, DAL.GenerateParameter("practiceNumber", practiceNumber));
            }

            return dataTable.Rows
                .Cast<DataRow>()
                .Select(dataRow => new LabInvoiceModel(dataRow))
                .ToList();
        }

        public static LabInvoiceModel GetByLedgerEntryId(int ledgerEntryId)
        {
            DataRow dataRow;

            using (DAL dal = new DAL("WarehouseNavision"))
            {
                dataRow = dal.GetDataRow(
                    "SELECT * FROM View_NavisionUnauthorisedInvoices WHERE [Ledger Entry Id] = @EntryNo",
                    DAL.GenerateParameter("EntryNo", ledgerEntryId));
            }

            if (dataRow == null)
            {
                throw new LabInvoiceMissingException(ledgerEntryId);
            }

            return new LabInvoiceModel(dataRow);
        }

        public static LabInvoiceModel GetLabInvoiceForPermissionCheck(List<int> ledgerEntryIds)
        {
            LabInvoiceModel matchingLabInvoice = null;
            int ledgerEntryIndex = 0;

            while (ledgerEntryIndex < ledgerEntryIds.Count && matchingLabInvoice == null)
            {
                int ledgerEntryId = ledgerEntryIds[ledgerEntryIndex];

                try
                {
                    matchingLabInvoice = GetByLedgerEntryId(ledgerEntryId);
                }
                catch (LabInvoiceMissingException)
                {
                }

                ledgerEntryIndex++;
            }

            if (matchingLabInvoice == null)
            {
                throw new LabInvoiceMissingException("No Invoices are there to calculate the Practice Id.");
            }

            return matchingLabInvoice;
        }

        public static int GetUnapprovedInvoicesCount()
        {
            int count;
            const string sql = "SELECT COUNT([Invoice Number]) FROM View_NavisionUnauthorisedInvoices";

            using (DAL dal = new DAL("WarehouseNavision"))
            {
                count = (int) dal.GetScalar(sql);
            }

            return count;
        }

        public static int GetUnapprovedInvoicesCount(int practiceNumber)
        {
            if (practiceNumber == 0)
            {
                return GetUnapprovedInvoicesCount();
            }

            const string sql =
                "SELECT COUNT([Invoice Number]) FROM View_NavisionUnauthorisedInvoices WHERE [Practice ID] = @practiceNumber";

            using (DAL dal = new DAL("WarehouseNavision"))
            {
                return (int) dal.GetScalar(sql, DAL.GenerateParameter("practiceNumber", practiceNumber));
            }
        }

        public static bool HasPermission(int practiceIdToBeAuthorised, WisdomUser currentUser)
        {
            return currentUser.IsAdm() ||
                   currentUser.IsClaritySuperUser() ||
                   currentUser.IsPm() && practiceIdToBeAuthorised.ToString() == currentUser.GetLocationForLabInvoices();
        }

        public void SetOnHoldStatus(InvoiceOnHoldStatus onHoldStatus, WisdomUser currentUser)
        {
            UpdateWithOnHoldStatus(onHoldStatus);
            AddAuditForQueryingInvoices(onHoldStatus, currentUser);
        }

        private void AddAuditForQueryingInvoices(InvoiceOnHoldStatus onHoldStatus, WisdomUser currentUser)
        {
            Log.Debug("Practice View", $"About to try and add an audit log for querying Ledger Id {LedgerEntryId}");

            string onHoldDbValue = StatusToDbValue[onHoldStatus];

            List<DbParameter> parameters = new List<DbParameter>
            {
                DAL.GenerateParameter("@RecordID", InvoiceNumber),
                DAL.GenerateParameter("@OldValue", "Y"),
                DAL.GenerateParameter("@NewValue", onHoldDbValue),
                DAL.GenerateParameter("@Field", "On Hold"),
                DAL.GenerateParameter("@DateTimeUpdated", DateTime.Now),
                DAL.GenerateParameter("@UserUpdated", currentUser.GetAdUsername()),
                DAL.GenerateParameter("@LedgerEntryId", LedgerEntryId)
            };

            const string sql =
                "INSERT INTO dbo.LabAuthorisationAudit (RecordID, OldValue, NewValue, Field, DateTimeUpdated, UserUpdated, LedgerEntryId) VALUES (@RecordID, @OldValue, @NewValue, @Field, @DateTimeUpdated, @UserUpdated, @LedgerEntryId)";

            using (DAL dal = new DAL("WarehouseNavision"))
            {
                dal.ExecuteNonQuery(sql, parameters);
            }

            Log.Debug("Practice View", $"Successfully added an audit log for querying Ledger Id {LedgerEntryId}");
        }

        private void UpdateWithOnHoldStatus(InvoiceOnHoldStatus onHoldStatus)
        {
            Log.Debug("Practice View", $"About to try and execute update Ledger Id {LedgerEntryId}");

            string onHoldDbValue = StatusToDbValue[onHoldStatus];

            List<DbParameter> parameters = new List<DbParameter>
            {
                DAL.GenerateParameter("@OnHoldOld", OnHold),
                DAL.GenerateParameter("@OnHold", onHoldDbValue),
                DAL.GenerateParameter("@EntryNo", LedgerEntryId)
            };

            const string sql =
                "UPDATE [Petrie Tucker & Partners Ltd$Vendor Ledger Entry] SET [On Hold] = @OnHold WHERE [Entry No_] = @EntryNo AND [On Hold] = @OnHoldOld";

            using (DAL dal = new DAL("Navision"))
            {
                dal.ExecuteNonQuery(sql, parameters);
            }

            Log.Debug("Practice View", $"Successfully updated Ledger Id {LedgerEntryId}");
        }
    }
}
```

#### 2.1.2. `LabInvoiceAuditModel.cs`

```csharp
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Linq;
using System.Text;
using IDHGroup.SharedLibraries.DataAccessLayer;
using IDHGroup.UI.PracticeView.Helpers.Extensions;

namespace IDHGroup.UI.PracticeView.Models
{
    public class LabInvoiceAuditModel
    {
        public LabInvoiceAuditModel(DataRow dataRow)
        {
            LabAuthorisationId = dataRow.GetColumnValueOrDefault<int>("LabAuthorisationAuditId");
            RecordID = dataRow.GetColumnValueOrDefault<string>("RecordId");
            OldValue = dataRow.GetColumnValueOrDefault<string>("OldValue");
            NewValue = dataRow.GetColumnValueOrDefault<string>("NewValue");
            Field = dataRow.GetColumnValueOrDefault<string>("Field");
            UserUpdated = dataRow.GetColumnValueOrDefault<string>("UserUpdated");
            LedgerEntryId = dataRow.GetColumnValueOrDefault<int?>("LedgerEntryId");
            LabCode = dataRow.GetColumnValueOrDefault<string>("LabCode");
            LabName = dataRow.GetColumnValueOrDefault<string>("LabName");
            DateTimeUpdated = dataRow.GetColumnValueOrDefault<DateTime>("DateTimeUpdated");
        }

        public DateTime DateTimeUpdated { get; set; }
        public string Field { get; set; }
        public int LabAuthorisationId { get; set; }
        public string LabCode { get; set; }
        public string LabName { get; set; }
        public int? LedgerEntryId { get; set; }
        public string NewValue { get; set; }
        public string OldValue { get; set; }
        public string RecordID { get; set; }
        public string UserUpdated { get; set; }

        public static List<LabInvoiceAuditModel> SearchForLabAuthorisations(string recordID, DateTime? startDateTime, DateTime? endDateTime, string userUpdated)
        {
            DataTable dataTable;

            string sql = GetSearchSql(recordID, startDateTime, endDateTime, userUpdated);

            List<DbParameter> parameters = GetSearchDbParameters(recordID, startDateTime, endDateTime, userUpdated);

            using (DAL dal = new DAL("WarehouseNavision"))
            {
                dataTable = dal.GetDataTable(sql, parameters);
            }

            return dataTable.Rows
                .Cast<DataRow>()
                .Select(dataRow => new LabInvoiceAuditModel(dataRow))
                .ToList();
        }

        private static List<DbParameter> GetSearchDbParameters(string recordId, DateTime? startDateTime,
            DateTime? endDateTime, string userUpdated)
        {
            List<DbParameter> parameters = new List<DbParameter>();

            if (!string.IsNullOrEmpty(recordId))
            {
                parameters.Add(DAL.GenerateParameter("RecordID", recordId));
            }

            if (!string.IsNullOrEmpty(userUpdated))
            {
                parameters.Add(DAL.GenerateParameter("UserUpdated", userUpdated));
            }

            if (null != startDateTime)
            {
                parameters.Add(DAL.GenerateParameter("StartDateTime", startDateTime));
            }

            if (null != endDateTime)
            {
                parameters.Add(DAL.GenerateParameter("EndDateTime", endDateTime));
            }

            return parameters;
        }

        private static string GetSearchSql(string recordId, DateTime? startDateTime, DateTime? endDateTime,
            string userUpdated)
        {
            StringBuilder stringBuilder = new StringBuilder(@"SELECT [LabAuthorisationAuditID]
                      ,[RecordID]
                      ,[LedgerEntryId]
                      ,[OldValue]
                      ,[NewValue]
                      ,[Field]
                      ,[DateTimeUpdated]
                      ,[UserUpdated]
                      , head.[Vendor Invoice No_] as [Invoice Number]
                      , head.[Pay-to Vendor No_] as [LabCode]
                      , head.[Pay-to Name] as [LabName]
                  FROM [Warehouse].[dbo].[LabAuthorisationAudit] LA
                  LEFT JOIN SQL5.Nav.[dbo].[Petrie Tucker & Partners Ltd$Vendor Ledger Entry] vend WITH(NOLOCK)  ON vend.[Entry No_] = LA.LedgerEntryId
                  LEFT JOIN SQL5.Nav.[dbo].[Petrie Tucker & Partners Ltd$Purch_ Inv_ Header] head WITH(NOLOCK) ON head.No_ = vend.[Document No_] WHERE 1=1 ");

            if (!string.IsNullOrEmpty(recordId))
            {
                stringBuilder.Append("AND RecordID LIKE '%' + @RecordID + '%' ");
            }

            if (!string.IsNullOrEmpty(userUpdated))
            {
                stringBuilder.Append("AND UserUpdated LIKE '%' + @UserUpdated + '%' ");
            }

            if (null != startDateTime)
            {
                stringBuilder.Append("AND DateTimeUpdated > @StartDateTime ");
            }

            if (null != endDateTime)
            {
                stringBuilder.Append("AND DateTimeUpdated < @EndDateTime ");
            }

            return stringBuilder.ToString();
        }
    }
}
```

#### 2.1.3. `LabInvoiceSummaryModel.cs`

```csharp
using System.Text;
using IDHGroup.SharedLibraries.DataAccessLayer;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Runtime.Serialization;
using System.Web;

namespace IDHGroup.UI.PracticeView.Models
{
    [DataContractAttribute]
    public class LabInvoiceSummaryModel
    {
        public LabInvoiceSummaryModel(int practiceId, string practiceName, int count, int inQuery)
        {
            this.PracticeId = practiceId;
            this.PracticeName = practiceName;
            this.Count = count;
        }
        public LabInvoiceSummaryModel(DataRow dataRow, Dictionary<int, string> practices)
        {
            this.PracticeId = int.Parse(dataRow["practiceId"].ToString());
            this.Count = int.Parse(dataRow["Count"].ToString());
            this.InQuery = int.Parse(dataRow["Inquery"].ToString());

            if (practices.ContainsKey(this.PracticeId))
                this.PracticeName = practices[this.PracticeId];
        }

        public static List<LabInvoiceSummaryModel> GetUnapprovedInvoicesCount(Dictionary<int, string> allPractices, List<int> filterPractices = null)
        {
            List<LabInvoiceSummaryModel> labInvoiceSummaryModel = new List<LabInvoiceSummaryModel>();
            DataTable dataTable = new DataTable();
            StringBuilder inSQL = new StringBuilder();

            if (filterPractices.Count != 0)
            {
                foreach (int practiceId in filterPractices) inSQL.Append(practiceId + ",");
                inSQL.Remove(inSQL.Length - 1, 1);
            }

            using (DAL dal = new DAL("WarehouseNavision"))
            {
                if (filterPractices.Count != 0)
                    dataTable = dal.GetDataTable(string.Format("SELECT COUNT([Invoice Number]) AS Count, SUM(CASE LEN([On Hold]) WHEN 1 THEN 0 ELSE 1 END) AS Inquery,[Practice Id] AS PracticeId FROM View_NavisionUnauthorisedInvoices WHERE [Practice Id] IN ({0}) GROUP BY [Practice Id]", inSQL.ToString()));
                else
                    dataTable = dal.GetDataTable("SELECT COUNT([Invoice Number]) AS Count, SUM(CASE LEN([On Hold]) WHEN 1 THEN 0 ELSE 1 END) AS Inquery,[Practice Id] AS PracticeId FROM View_NavisionUnauthorisedInvoices GROUP BY [Practice Id]");
            }

            foreach (DataRow dataRow in dataTable.Rows)
            {
                LabInvoiceSummaryModel labInvoicesSummaryModel = new LabInvoiceSummaryModel(dataRow, allPractices);

                if(!string.IsNullOrEmpty(labInvoicesSummaryModel.PracticeName))
                    labInvoiceSummaryModel.Add(labInvoicesSummaryModel);
            }

            foreach (int practiceId in filterPractices)
                if (!dataTable.AsEnumerable().Any(row => practiceId == row.Field<int>("PracticeId")))
                {
                    DataRow dataRow = dataTable.NewRow();
                    dataRow["PracticeId"] = practiceId;
                    dataRow["Count"] = 0;
                    dataRow["Inquery"] = 0;

                    labInvoiceSummaryModel.Add(new LabInvoiceSummaryModel(dataRow, allPractices));
                }

            return labInvoiceSummaryModel;
        }

        public static List<LabInvoiceSummaryModel> GetUnapprovedInvoicesCount(List<int> practiceNumbers, Dictionary<int, string> allPractices)
        {
            List<LabInvoiceSummaryModel> labInvoiceSummaryModel = new List<LabInvoiceSummaryModel>();
            DataTable dataTable = new DataTable();
            StringBuilder text = new StringBuilder();

            foreach (int practiceId in practiceNumbers)
                text.Append(practiceId + ",");

            text.Remove(text.Length - 1, 1);

            using (DAL dal = new DAL("WarehouseNavision"))
                    dataTable = dal.GetDataTable(string.Format("SELECT COUNT([Invoice Number]) AS Count, SUM(CASE LEN([On Hold]) WHEN 1 THEN 0 ELSE 1 END) AS Inquery,[Practice Id] AS PracticeId FROM View_NavisionUnauthorisedInvoices GROUP BY [Practice Id]", text.ToString()));

            foreach (DataRow dataRow in dataTable.Rows)
            {
                LabInvoiceSummaryModel labInvoicesSummaryModel = new LabInvoiceSummaryModel(dataRow, allPractices);

                if (!string.IsNullOrEmpty(labInvoicesSummaryModel.PracticeName))
                    labInvoiceSummaryModel.Add(labInvoicesSummaryModel);
            }

            foreach (int practiceId in practiceNumbers)
                if(!dataTable.AsEnumerable().Any(row => practiceId == row.Field<int>("PracticeId")))
                {
                    DataRow dataRow = dataTable.NewRow();
                    dataRow["PracticeId"] = practiceId;
                    dataRow["Count"] = 0;
                    dataRow["Inquery"] = 0;

                    labInvoiceSummaryModel.Add(new LabInvoiceSummaryModel(dataRow, allPractices));
                }

            return labInvoiceSummaryModel;
        }

        [DataMember]
        public int PracticeId { get; private set; }
        [DataMember]
        public string PracticeName { get; set; }
        [DataMember]
        public int Count{ get; private set; }
        [DataMember]
        public int InQuery { get; private set; }
    }
}
```

### 2.2. Controllers

Create the following C# controller classes:

#### 2.2.1. `LabInvoiceController.cs`

```csharp
using System;
using System.Collections.Generic;
using System.Text;
using System.Web.Mvc;
using IDHGroup.SharedLibraries.Logging;
using IDHGroup.UI.PracticeView.Enums;
using IDHGroup.UI.PracticeView.Extensions;
using IDHGroup.UI.PracticeView.Helpers;
using IDHGroup.UI.PracticeView.Models;

namespace IDHGroup.UI.PracticeView.Controllers
{
    public class LabInvoiceController : BaseController
    {
        public JsonResult PlaceOnHold(List<int> ledgerEntryIds)
        {
            StringBuilder ledgers = new StringBuilder();

            try
            {
                foreach (int ledgerEntryId in ledgerEntryIds)
                {
                    ledgers.Append(ledgerEntryId + ",");
                }

                Log.Debug("Practice View", string.Format(
                    "About to try and put one or more invoices On Hold in Navision. User - {0}, their PracticeId is {1} and the Invoices they're placing onhold are {2}.",
                    CurrentUser.GetAdUsername(), CurrentUser.GetAdUsername(), ledgers));

                LabInvoiceModel labInvoiceModelIndividual = LabInvoiceModel.GetLabInvoiceForPermissionCheck(ledgerEntryIds);

                bool userIsOverridden = CurrentUser.GetIsOverridden();

                if (!LabInvoiceModel.HasPermission(labInvoiceModelIndividual.PracticeNumber, CurrentUser))
                {
                    return Json(new
                    {
                        IsSuccess = false,
                        Message = string.Format(
                            "Unfortunately you are not able to place these invoices on hold, only the Practice Manager of Site {0} or an Area Manager is able to do this.  You are currently logged in as {1} and your Practice Number is set to '{2}'.  If you are the Practice Manager for this Practice please log into the computer with the correct account, if the Practice Number is blank please contact IT.",
                            labInvoiceModelIndividual.PracticeNumber == 0
                                ? "blank"
                                : labInvoiceModelIndividual.PracticeNumber.ToString(), CurrentUser.GetUsername(),
                            CurrentUser.GetLocationForLabInvoices())
                    });
                }

                foreach (int ledgerEntryId in ledgerEntryIds)
                {
                    try
                    {
                        LabInvoiceModel labInvoiceModel = LabInvoiceModel.GetByLedgerEntryId(ledgerEntryId);
                        labInvoiceModel.SetOnHoldStatus(InvoiceOnHoldStatus.Queried, CurrentUser);
                    }
                    catch (LabInvoiceMissingException)
                    {
                        //Silent Exception - invoice will already have been approved.
                    }
                }
            }
            catch (LabInvoiceMissingException ex)
            {
                Log.Error("Practice View", string.Format(
                    "Unable to calculate PracticeId from Invoices. User - {0}, their PracticeId is {1} and the Invoices they're approving are {2}.",
                    CurrentUser.GetAdUsername(), CurrentUser.GetLocationForLabInvoices(), ledgers), ex);

                return Json(new {IsSuccess = false, Message = ex.Message});
            }
            catch (Exception ex)
            {
                Log.Error("Practice View", string.Format(
                    "Exception when trying to add Query Flag for one or many Lab Invoices in Navision. User - {0}, their PracticeId is {1} and the Invoices they're approving are {2}.",
                    CurrentUser.GetAdUsername(), CurrentUser.GetLocationForLabInvoices(), ledgers), ex);

                return Json(new {IsSuccess = false, Message = ex.Message});
            }

            return Json(new {IsSuccess = true, Message = true, JsonRequestBehavior.AllowGet});
        }

        public JsonResult RemoveOnHoldFlag(List<int> ledgerEntryIds)
        {
            StringBuilder ledgers = new StringBuilder();

            try
            {

                foreach (int ledgerEntryId in ledgerEntryIds)
                {
                    ledgers.Append(ledgerEntryId + ",");
                }

                Log.Debug("Practice View", string.Format(
                    "About to try and remove an OnHold Flag for one or many Lab Invoices in Navision. User - {0}, their PracticeId is {1} and the Invoices they're approving are {2}.",
                    CurrentUser.GetAdUsername(), CurrentUser.GetAdUsername(), ledgers));

                LabInvoiceModel labInvoiceModelIndividual = LabInvoiceModel.GetLabInvoiceForPermissionCheck(ledgerEntryIds);

                bool isOverridden = CurrentUser.GetIsOverridden();

                if (!LabInvoiceModel.HasPermission(labInvoiceModelIndividual.PracticeNumber, CurrentUser))
                {
                    string practiceId = labInvoiceModelIndividual.PracticeNumber == 0
                        ? "blank"
                        : labInvoiceModelIndividual.PracticeNumber.ToString();

                    return Json(new
                    {
                        IsSuccess = false,
                        Message = string.Format(
                            "Unfortunately you are not able to approve these invoices, only the Practice Manager of Site {0} or an Area Manager is able to do this.  You are currently logged in as {1} and your Practice Number is set to '{2}'.  If you are the Practice Manager for this Practice please log into the computer with the correct account, if the Practice Number is blank please contact IT.",
                            practiceId, CurrentUser.GetUsername(), CurrentUser.GetLocationForLabInvoices())
                    });
                }

                foreach (int ledgerEntryId in ledgerEntryIds)
                {
                    try
                    {
                        LabInvoiceModel labInvoiceModel = LabInvoiceModel.GetByLedgerEntryId(ledgerEntryId);
                        labInvoiceModel.SetOnHoldStatus(InvoiceOnHoldStatus.NotOnHold, CurrentUser);
                    }
                    catch (LabInvoiceMissingException)
                    {
                        //Silent Exception - invoice will already have been approved.
                    }
                }
            }
            catch (LabInvoiceMissingException ex)
            {
                Log.Error("Practice View", string.Format(
                    "Unable to calculate PracticeId from Invoices. User - {0}, their PracticeId is {1} and the Invoices they're approving are {2}.",
                    CurrentUser.GetAdUsername(),
                    CurrentUser.GetLocationForLabInvoices(), ledgers), ex);

                return Json(new {IsSuccess = false, Message = ex.Message});
            }
            catch (Exception ex)
            {
                Log.Error("Practice View", string.Format(
                    "Exception when trying to remove an OnHold Flag for one or many Lab Invoices in Navision. User - {0}, their PracticeId is {1} and the Invoices they're approving are {2}.",
                    CurrentUser.GetAdUsername(),
                    CurrentUser.GetLocationForLabInvoices(), ledgers), ex);

                return Json(new {IsSuccess = false, Message = ex.Message});
            }

            return Json(new {IsSuccess = true, Message = true, JsonRequestBehavior.AllowGet});
        }
    }
}
```

#### 2.2.2. `LabInvoiceAuditController.cs`

```csharp
using System.Web.Mvc;

namespace IDHGroup.UI.PracticeView.Controllers
{
    public class LabInvoiceAuditController : BaseController
    {
        public ActionResult ViewReport()
        {
            return View();
        }
    }
}
```

### 2.3. API Controllers

Create the following C# API controller classes:

#### 2.3.1. `LabInvoiceController.cs`

```csharp
using System.Collections.Generic;
using System.Linq;
using System.Web.Http;
using IDHGroup.UI.PracticeView.Models;

namespace IDHGroup.UI.PracticeView.Controllers.API
{
    public class LabInvoiceController : ApiController
    {
        public IEnumerable<LabInvoiceModel> Get(int practiceId)
        {
            return LabInvoiceModel.GetAllUnapprovedInvoices(practiceId);
        }

        ///PracticeView/api/LabInvoice/GetUnapprovedCount
        [HttpPost]
        public IEnumerable<LabInvoiceSummaryModel> GetUnapprovedCount(GetUnapprovedCountRequest request)
        {
            Dictionary<int, string> practices = new Dictionary<int, string>();

            foreach (string str in request.Practices)
            {
                int id = int.Parse(str.Substring(0, str.IndexOf("|")));
                string name = str.Substring(str.IndexOf("|") + 1, str.Length - (str.IndexOf("|") + 1));

                practices.Add(id, name);
            }

            if (request.PracticeQuery == null) //Get Everything
            {
                return LabInvoiceSummaryModel.GetUnapprovedInvoicesCount(practices)
                    .OrderBy(l => l.PracticeName);
            }

            int practiceId = 0;

            bool isInt = int.TryParse(request.PracticeQuery, out practiceId);

            if (isInt)
            {
                List<int> practiceIds = (from practice in practices
                    where practice.Key.ToString()
                        .Contains(request.PracticeQuery)
                    select practice.Key).ToList();

                if (practiceIds.Count == 0)
                {
                    return null;
                }

                return LabInvoiceSummaryModel.GetUnapprovedInvoicesCount(practices, practiceIds)
                    .OrderBy(l => l.PracticeName);
            }
            else
            {
                List<int> practiceIds = (from practice in practices
                    where practice.Value.ToLower()
                        .Contains(request.PracticeQuery)
                    select practice.Key).ToList();

                if (practiceIds.Count == 0)
                {
                    return null;
                }

                return LabInvoiceSummaryModel.GetUnapprovedInvoicesCount(practiceIds, practices)
                    .OrderBy(l => l.PracticeName);
            }
        }
    }
}
```

#### 2.3.2. `LabInvoiceAuditController.cs`

```csharp
using System;
using System.Collections.Generic;
using IDHGroup.UI.PracticeView.Models;

namespace IDHGroup.UI.PracticeView.Controllers.API
{
    public class LabInvoiceAuditController : BaseApiController
    {
        ///PracticeView/api/LabInvoiceAudit?startDateTime=2016-01-01 11:11&endDateTime=2016-01-04 11:11&updatedUser=wxho\wisbech-pm
        public IEnumerable<LabInvoiceAuditModel> Get(string recordId = null, DateTime? startDateTime = null, DateTime? endDateTime = null)
        {
            return LabInvoiceAuditModel.SearchForLabAuthorisations(recordId, startDateTime, endDateTime, CurrentUser.GetAdUsername());
        }
    }
}
```

### 2.4. Data Access

The data access logic is contained within the models themselves. The following SQL views and tables are required:

*   **`View_NavisionUnauthorisedInvoices`** (in `WarehouseNavision`): This view should return a list of unapproved lab invoices.
*   **`LabAuthorisationAudit`** (in `WarehouseNavision`): This table stores the audit trail for lab invoice authorizations.
*   **`Petrie Tucker & Partners Ltd$Vendor Ledger Entry`** (in `Navision`): This table contains vendor ledger entries.
*   **`Petrie Tucker & Partners Ltd$Purch_ Inv_ Header`** (in `Navision`): This table contains purchase invoice headers.

## 3. Frontend

### 3.1. Views

Create the following Razor view:

#### 3.1.1. `ViewReport.cshtml`

Create a file at `Views/LabInvoiceAudit/ViewReport.cshtml`:

```html
@section Scripts{

    @Scripts.Render("~/bundles/viewreport")
}


@{
    ViewBag.Title = "View Report";
    Layout = "~/Views/Shared/_LayoutBlank.cshtml";
}

<style>
    .table-searchfilter{
        width : 50%;
    }
    .table-searchfilter td {
        padding : 2px 5px 2px 5px;
    }

    .table-searchfilter input {
        padding : 2px 5px 2px 5px;
    }

    .table-searchfilter input:focus{
        box-shadow: #7e3b70;
    }

    #svg-type {
        width:32px;
        display:inline;
        cursor:pointer;
    }

    #span-description{
        font-size:0.5em;
    }

    #div-svgwrapper {
        margin: 0 auto;
        cursor: pointer;
    }

</style>

<div id="_viewContainer">

    <div class="formContainer">

        <h1 class="formContainer_title">
            Lab Invoice Reporting
            <img src="~/Core/Images/Clipboard.png" id="svg-type" />
            <span id="span-description">Invoices that have been approved</span>
        </h1>

    </div>

    <div data-bind="visible: IsLoading()">

        <div class="loader">
            <h1>Doing a bit of a Search...</h1>
            <div>
                <img src="@Url.Content("~/Core/Images/loading.gif")" />
            </div>
        </div>

    </div>

    <div class="formContainer_title-smaller formContainer_title-view" id="div-audit-filters">
        <table class="table-searchfilter" id="table-audit-filters">
            <tr>
                <td>Record Id</td>
                <td><input type="text" id="input-text-user" data-bind="value : RecordId" /></td>
            </tr>
            <tr>
                <td>Start Date Time</td>
                <td><input type="text" id="_startDateTime" data-bind="value : StartDateTime" /></td>
            </tr>
            <tr>
                <td>End Date Time</td>
                <td><input type="text" id="_endDateTime" data-bind="value : EndDateTime" /></td>
            </tr>
            <tr>
                <td></td>
                <td></td>
            </tr>
            <tfoot>
                <tr>
                    <td colspan="2">
                        <button id="_buttonSearchAudits" class="k-button k-button-icontext k-grid-View" data-bind="click : SearchAudits">Search</button>
                    </td>
                </tr>
            </tfoot>
        </table>
    </div>

    <div class="formContainer_title-smaller formContainer_title-view" id="div-invoice-filters">
        <table class="table-searchfilter" id="table-invoice-filters">
            <tr>
                <td>Practice</td>
                <td><input type="text" id="input-text-user" data-bind="value : PracticeQuery" /></td>
            </tr>
            <tr>
                <td>
                    <button id="_buttonSearchInvoices" class="k-button k-button-icontext k-grid-View" data-bind="click : SearchInvoices">Search</button>
                </td>
                <td>
                    <div id="div-feedback-message"></div>
                </td>
            </tr>
        </table>
    </div>

    <div class="grid" data-bind="kendoGrid: LabAuditInvoicesSearchGridOptions" id="grid-audits"></div>

    <div class="grid" data-bind="kendoGrid: LabInvoicesSearchGridOptions" id="grid-invoices"></div>

</div>
```

### 3.2. JavaScript

Create the following JavaScript files:

#### 3.2.1. `LabInvoicesBindings.js`

Create a file at `Core/Scripts/KnockoutModels/LabInvoices/LabInvoicesBindings.js`:

```javascript
function LabInvoiceAudit(id, recordId, oldValue, newValue, fieldId, dateTimeUpdated, userUpdated, labCode, labName) {
    var self = this;

    self.id = id;
    self.recordId = recordId;
    self.oldValue = oldValue;
    self.newValue = newValue;
    self.fieldId = fieldId;
    self.dateTimeUpdated = dateTimeUpdated;
    self.userUpdated = userUpdated;
    self.labCode = labCode;
    self.labName = labName;
}

function SearchLabInvoiceAuditViewModel(user) {
    var self = this;
    var ajaxActions = new AjaxActions(user);

    var date = new Date();
    date.setDate(date.getDate() - 7);

    self.RecordId = ko.observable('');
    self.StartDateTime = ko.observable(formattedDate(date, '9', '0'));
    self.EndDateTime = ko.observable(formattedDate(new Date(), '17', '30'));
    self.LabInvoiceAudit = ko.observableArray([]);
    self.IsLoading = ko.observable(false);

    self.UrlAudits = ko.observable("../api/LabInvoiceAudit");

    // methods
    self.SearchAudits = function () {
        self.IsLoading(true);
        $.ajax({
            url: self.UrlAudits(),
            data: {
                recordId: self.RecordId(),
                startDateTime: self.StartDateTime(),
                endDateTime: self.EndDateTime()
            },
            type: "GET",
            success: function (data) {
                self.IsLoading(false);

                if(data.length > 0)
                    self.LabInvoiceAudit(data);
            }
        });
    }

    self.LabAuditInvoicesSearchGridOptions = {
        toolbar: ["excel"],
        excel: {
            fileName: "Approved Lab Invoices.xlsx",
            filterable: true,
            allPages: true
        },
        data: self.LabInvoiceAudit,
        columns: [
            { title: "Id", field: "LabAuthorisationId" },
            { title: "Record ID", field: "RecordID" },
            { title: "Old Value", field: "OldValue" },
            { title: "New Value", field: "NewValue" },
            { title: "Field", field: "Field" },
            { title: "Lab Code", field: "LabCode" },
            { title: "Lab Name", field: "LabName" },
            {
                title: "Date Updated", field: "DateTimeUpdated",
                template: "#= kendo.toString(kendo.parseDate(DateTimeUpdated, 'yyyy-MM-ddT'), 'dd/MM/yyyy') #"
            },
            { title: "User", field: "UserUpdated" }
        ],
        pageable: {
            pageSize: 25
        },
        sortable: true,
        groupable: false
    };


    self.PracticeQuery = ko.observable('');
    self.LabInvoices = ko.observableArray([]);
    self.Practices = ko.observableArray([]);

    self.UrlInvoices = ko.observable("../api/LabInvoice/GetUnapprovedCount");

    // methods
    self.SearchInvoices = function () {

        self.IsLoading(true);
        ajaxActions.Get("Practices?$select=Id,PracticeName")
            .then(function (practices) {

                self.Practices(practices);
                var dictionary = [];

                $.each(practices.value,
                    function(i, item) {
                        dictionary.push(item.Id + '|' + item.PracticeName);
                    });

                $.ajax({
                    url: self.UrlInvoices(),
                    data: JSON.stringify({
                        Practices: dictionary,
                        PracticeQuery: self.PracticeQuery()
                    }),
                    type: 'POST',
                    contentType: 'application/json; charset=utf-8',
                    dataType: 'json',
                    success: function(data) {
                        self.IsLoading(false);

                        if (data != null && data.length > 0) {
                            $('#grid-invoices').show();
                            $('#div-feedback-message').hide();
                            $('#div-feedback-message').text('');

                            self.LabInvoices(data);
                        } else {
                            $('#grid-invoices').hide();
                            $('#div-feedback-message').show();
                            $('#div-feedback-message').text('No Practices were found matching the text ' + self.PracticeQuery());
                        }
                    },
                    error: function(a, b, c) {

                        Notify("Could not retrieve invoices. Reason: " + c);
                    }
                });
            })
            .fail(function(error) {

                Notify("Could not retrieve list of practices. Reason: " + error, "error");
            });
    }


    self.LabInvoicesSearchGridOptions = {
        toolbar: ["excel"],
        excel: {
            fileName: "Unapproved Lab Invoices.xlsx",
            filterable: true,
            allPages: true
        },
        data: self.LabInvoices,
        columns: [
            { command: { text: "View Invoices", click: redirectToLabInvoices }, title: " "},
            { title: "Practice Number", field: "PracticeId" },
            { title: "Practice Name", field: "PracticeName" },
            { title: "Count of Unapproved Invoices", field: "Count" },
            { title: "Number In Query", field: "InQuery" }
        ],
        pageable: {
            pageSize: 25
        },
        sortable: true,
        groupable: false
    };
};

$(document).ready(function () {

    var hash = window.location.hash;
    var pageMode = hash == null || hash == 'undefined' || hash == '' ? 'Invoice' : hash;

    $('#_startDateTime').datetimepicker({ datepicker: true, mask: '9999/19/39 29:59', format: 'Y-m-d H:i', "option": { minDate: null, maxDate: null } }).keyup(function (e) {
        if (e.keyCode === 8 || e.keyCode === 46) {
            $(this).val(null);
        }
    });
    $('#_endDateTime').datetimepicker({ datepicker: true, mask: '9999/19/39 29:59', format: 'Y-m-d H:i', "option": { minDate: null, maxDate: null } }).keyup(function (e) {
        if (e.keyCode === 8 || e.keyCode === 46) {
            $(this).val(null);
        }
    });

    $('#div-invoice-filters').hide();
    $('#grid-invoices').hide();

    $('#svg-type').click(function () {
        $('#div-invoice-filters').toggle();
        $('#div-audit-filters').toggle();
        $('#grid-audits').toggle();
        $('#grid-invoices').toggle();

        var isInvoice = this.src.indexOf('BankNote.png') != -1;
        this.src = isInvoice ? this.src.replace('BankNote.png', 'Clipboard.png') : this.src.replace('Clipboard.png', 'BankNote.png');
        $('#span-description').text(isInvoice ? 'Invoices that have been approved' : 'Invoices yet to be approved');
    });

    //Get User Permissions before anything else happens
    UserPermissions.getInitialisedPermissionsAsync('LabInvoicesBindings')
        .then(function(user) {

            var $appContainer = $("#_formAppContainer");
            var searchLabInvoiceAuditViewModel = new SearchLabInvoiceAuditViewModel(user);

            ko.applyBindings(searchLabInvoiceAuditViewModel, $appContainer[0]);
        });


    if (pageMode == 'Invoice') {
        $('#svg-type').trigger("click");
    }
});

function formattedDate(date, hourOverride, minutesOverride) {
    var d = new Date(date || Date.now()),
        month = '' + (d.getMonth() + 1),
        day = '' + d.getDate(),
        year = d.getFullYear(),
        hour = '' + d.getHours(),
        minute = '' + d.getMinutes();

    if (hourOverride != undefined)
        hour = '' + hourOverride;

    if (minutesOverride != undefined)
        minute = '' + minutesOverride;

    if (month.length < 2) month = '0' + month;
    if (day.length < 2) day = '0' + day;
    if (hour.length < 2) hour = '0' + hour;
    if (minute.length < 2) minute = '0' + minute;

    return [year, month, day].join('-') + ' ' + [hour, minute].join(':');
}

function redirectToLabInvoices(e) {
    e.preventDefault();

    var dataItem = this.dataItem($(e.currentTarget).closest("tr"));
    window.location.href = "../Practice/Details/" + dataItem.PracticeId + "#1";
}
```

#### 3.2.2. `LabInvoicesBindingsPartial.js`

Create a file at `Core/Scripts/KnockoutModels/LabInvoices/LabInvoicesBindingsPartial.js`:

```javascript
$(document).ready(function () {
    $("#_invoiceGrid").kendoTooltip({
        filter: "td, th",
        show: function (e) {
            if (this.content.text() != "") {
                $('[role="tooltip"]').css("visibility", "visible");
            }
        },
        hide: function () {
            $('[role="tooltip"]').css("visibility", "hidden");
        },
        content: function (e) {
            var element = e.target[0];
            if (element.offsetWidth < element.scrollWidth) {
                return e.target.text();
            } else {
                return "";
            }
        }
    });
});
```

## 4. Configuration

### 4.1. `BundleConfig.cs`

In `App_Start/BundleConfig.cs`, register a new script bundle for the lab invoice reporting page:

```csharp
bundles.Add(new ScriptBundle("~/bundles/viewreport")
    .Include(
        "~/Core/Scripts/KnockoutModels/LabInvoices/LabInvoicesBindings.js",
        "~/Core/Scripts/KnockoutModels/LabInvoices/LabInvoicesBindingsPartial.js"
    ));
```

### 4.2. `RouteConfig.cs`

Ensure you have a default route configured in `App_Start/RouteConfig.cs` that can handle the controller actions.

### 4.3. `WebApiConfig.cs`

Ensure you have a default API route configured in `App_Start/WebApiConfig.cs` that can handle the API controller actions.

## 5. Shared Libraries

This project has dependencies on shared libraries: `IDHGroup.SharedLibraries.DataAccessLayer`, `IDHGroup.SharedLibraries.Logging`, and `IDHGroup.SharedLibraries.WisdomUsers`. You will need to ensure these libraries are available to the new project. You may need to set up a local NuGet feed or reference the projects directly.

## 6. Enums

The project uses an `InvoiceOnHoldStatus` enum. You will need to define this enum:

```csharp
namespace IDHGroup.UI.PracticeView.Enums
{
    public enum InvoiceOnHoldStatus
    {
        NotOnHold,
        Queried
    }
}
```

## 7. Helper Classes

The project uses several helper classes, such as `BaseController`, `BaseApiController`, `CurrentUser`, `DAL`, and various extension methods. You will need to replicate or reference these helper classes in your new project.

#### 7.1. `LabInvoiceMissingException.cs`

```csharp
using System;

namespace IDHGroup.UI.PracticeView.Helpers
{
    public class LabInvoiceMissingException : Exception
    {
        public LabInvoiceMissingException(string message)
            : base(message)
        {
        }

        public LabInvoiceMissingException(int ledgerEntryId) : base (string.Format("LedgerEntryId {0} has already been approved", ledgerEntryId))
        { 
        }

    }
}
```

#### 7.2. `DataRowExtension.cs`

```csharp
using System;
using System.Data;
using System.Linq;

namespace IDHGroup.UI.PracticeView.Helpers.Extensions
{
    public static class DataRowExtension
    {
        /// <summary>
        /// Returns DBNull if the datarow does not contain a value in the specified column or if the specified column does not exist in the datarow. Otherwise, returns the value stored in the column.
        /// </summary>
        /// <param name="dataRow">The datarow which will will retrieve a value from</param>
        /// <param name="columnName">The name of the column which is holding our value</param>
        /// <returns></returns>
        public static bool HasColumnValue(this DataRow dataRow, string columnName)
        {
            return dataRow.Table.Columns.Contains(columnName) && dataRow[columnName] != DBNull.Value;
        }

        public static T GetColumnValueOrDefault<T>(this DataRow dataRow, string columnName)
        {
            return GetColumnValueOrDefault<T>(dataRow, columnName, default(T));
        }

        public static T GetColumnValueOrDefault<T>(this DataRow dataRow, string columnName, T defaultValue)
        {
            if (!dataRow.HasColumnValue(columnName))
                return defaultValue;

            return (T)dataRow.GetColumnValue(columnName, typeof(T));
        }

        private static object GetColumnValue(this DataRow dataRow, string columnName, Type columnType)
        {
            // Column value will always be a primitive type. Get the code so we can use the right convert function for T's type
            TypeCode columnTypeCode = Type.GetTypeCode(columnType);

            switch (columnTypeCode)
            {
                case TypeCode.Boolean:
                    return Convert.ToBoolean(dataRow[columnName]);
                case TypeCode.Byte:
                    return Convert.ToInt32(dataRow[columnName]);
                case TypeCode.Char:
                    return Convert.ToChar(dataRow[columnName]);
                case TypeCode.Decimal:
                    return Convert.ToDecimal(dataRow[columnName]);
                case TypeCode.DateTime:
                    return Convert.ToDateTime(dataRow[columnName]);
                case TypeCode.Double:
                    return Convert.ToDouble(dataRow[columnName]);
                case TypeCode.Int16:
                    return Convert.ToInt16(dataRow[columnName]);
                case TypeCode.Int32:
                    return Convert.ToInt32(dataRow[columnName]);
                case TypeCode.Int64:
                    return Convert.ToInt64(dataRow[columnName]);
                case TypeCode.UInt16:
                    return Convert.ToUInt16(dataRow[columnName]);
                case TypeCode.UInt32:
                    return Convert.ToUInt32(dataRow[columnName]);
                case TypeCode.UInt64:
                    return Convert.ToUInt64(dataRow[columnName]);
                case TypeCode.SByte:
                    return Convert.ToSByte(dataRow[columnName]);
                case TypeCode.Single:
                    return Convert.ToSingle(dataRow[columnName]);
                case TypeCode.String:
                    return Convert.ToString(dataRow[columnName]);
                case TypeCode.Object:
                    return dataRow.GetImplementedColumnObject(columnName, columnType);
                default:
                    throw new NotImplementedException(string.Format(_exceptionMessage, columnType.FullName));
            }
        }

        private static object GetImplementedColumnObject(this DataRow dataRow, string columnName, Type columnType)
        {
            // DataRows support timespans so we should return these.
            if (columnType == typeof(TimeSpan) || columnType == typeof(TimeSpan?))
                return (TimeSpan)dataRow[columnName];

            if (columnType == typeof(Guid) || columnType == typeof(Guid?))
                return (Guid) dataRow[columnName];

            if (columnType == typeof(byte[]))
            {
                return (byte[]) dataRow[columnName];
            }

            Type columnBaseType = columnType.GenericTypeArguments.FirstOrDefault();

            // In case T is of type Object and not a subclass of Object, throw an exception
            if (columnBaseType == null)
                throw new NotImplementedException(string.Format(_exceptionMessage, columnType.FullName));

            // Otherwise, try to return the object from the type's subclasses
            return dataRow.GetColumnValue(columnName, columnBaseType);
        }

        private static string _exceptionMessage = "Cannot retrieve column value for {0}";
    }
}
```

#### 7.3. `GetUnapprovedCountRequest.cs`

```csharp
using System.Collections.Generic;

namespace IDHGroup.UI.PracticeView.Controllers.API
{
    public class GetUnapprovedCountRequest
    {
        public List<string> Practices;
        public string PracticeQuery;
    }
}
```

## 8. Tests

It is recommended to create a separate test project and add the following test class to ensure the functionality is working as expected.

### 8.1. `LabInvoiceAuditModelTest.cs`

```csharp
using System;
using System.Collections.Generic;
using System.Linq;
using IDHGroup.SharedLibraries.DataAccessLayer;
using IDHGroup.UI.PracticeView.Models;
using Microsoft.VisualStudio.TestTools.UnitTesting;

namespace PracticeView.Tests.Models
{
    [TestClass]
    [TestCategory("FeedbackModel")]
    public class LabInvoiceAuditModelTest
    {
        private const int TestPracticeId = 99999;

        [TestInitialize]
        public void TestInitialise()
        {
            using (DAL dbContext = new DAL("Warehouse"))
            {
                const string sql = @"
                    SELECT TOP(1) vend.[Entry No_]
                    INTO #InvoiceDetails
                    FROM SQL5.Nav.[dbo].[Petrie Tucker & Partners Ltd$Vendor Ledger Entry] vend WITH(NOLOCK)
                    INNER JOIN SQL5.Nav.[dbo].[Petrie Tucker & Partners Ltd$Purch_ Inv_ Header] head WITH(NOLOCK) ON head.No_ = vend.[Document No_]
                    WHERE head.[Vendor Invoice No_] != '' AND head.[Pay-to Vendor No_] != '' AND head.[Pay-to Name]  != ''

                    INSERT INTO [Warehouse].[dbo].[LabAuthorisationAudit] (RecordID, LedgerEntryId, OldValue, NewValue, Field, DateTimeUpdated, UserUpdated)
                    SELECT @TestPracticeId, #InvoiceDetails.[Entry No_], 'Y', 'Y QUERY', 'On Hold', GETDATE(), 'wxho\idhitdevelopment'
                    FROM #InvoiceDetails";

                dbContext.ExecuteNonQuery(sql, DAL.GenerateParameter("@TestPracticeId", TestPracticeId));
            }
        }

        [TestCleanup]
        public void TestCleanup()
        {
            using (DAL dbContext = new DAL("Warehouse"))
            {
                const string sql = @"DELETE FROM [Warehouse].[dbo].[LabAuthorisationAudit] WHERE RecordID = CAST(@TestPracticeId as VARCHAR)";

                dbContext.ExecuteNonQuery(sql, DAL.GenerateParameter("@TestPracticeId", TestPracticeId));
            }
        }

        [TestMethod]
        public void SearchForLabAuthorisations()
        {
            // Search by RecordID
            LabInvoiceAuditModel testLabInvoice = GenerateLabInvoiceAuditModel();
            Assert.AreEqual(TestPracticeId.ToString(), testLabInvoice.RecordID);
            Assert.AreEqual("Y", testLabInvoice.OldValue);
            Assert.AreEqual("Y QUERY", testLabInvoice.NewValue);
            Assert.AreEqual("On Hold", testLabInvoice.Field);
            Assert.AreEqual(@"wxho\idhitdevelopment", testLabInvoice.UserUpdated);
            Assert.IsNotNull(testLabInvoice.LedgerEntryId);
            Assert.IsNotNull(testLabInvoice.LabCode);
            Assert.IsNotNull(testLabInvoice.LabName);
            Assert.AreNotEqual(DateTime.MinValue, testLabInvoice.NewValue);

            // Search without filters
            ICollection<LabInvoiceAuditModel> labAuthorisations = LabInvoiceAuditModel.SearchForLabAuthorisations(null, null, null, null);

            Assert.IsNotNull(labAuthorisations);
            Assert.AreNotEqual(0, labAuthorisations.Count);

            // Search by start date time
            labAuthorisations = LabInvoiceAuditModel.SearchForLabAuthorisations(null, testLabInvoice.DateTimeUpdated.AddSeconds(-1), null, null);

            Assert.IsNotNull(labAuthorisations);
            Assert.AreNotEqual(0, labAuthorisations.Count);

            // Search by end date time
            labAuthorisations = LabInvoiceAuditModel.SearchForLabAuthorisations(null, null, testLabInvoice.DateTimeUpdated.AddSeconds(1), null);

            Assert.IsNotNull(labAuthorisations);
            Assert.AreNotEqual(0, labAuthorisations.Count);

            // Search by user updated
            labAuthorisations = LabInvoiceAuditModel.SearchForLabAuthorisations(null, null, null, testLabInvoice.UserUpdated);

            Assert.IsNotNull(labAuthorisations);
            Assert.AreEqual(1, labAuthorisations.Count);
        }

        private LabInvoiceAuditModel GenerateLabInvoiceAuditModel()
        {
            ICollection<LabInvoiceAuditModel> labAuthorisations = LabInvoiceAuditModel.SearchForLabAuthorisations(TestPracticeId.ToString(), null, null, null);
            LabInvoiceAuditModel labInvoice = labAuthorisations.FirstOrDefault();

            Assert.IsNotNull(labInvoice);

            return labInvoice;
        }
    }
}
```

## 9. Lab Invoices Grid UI

The main lab invoices grid is displayed on the `Practice/Details` page. The following files are used to create the UI:

*   `Views/Practice/Details.cshtml`: The main view that loads the lab invoices tab.
*   `Views/Practice/Partials/_Invoices.cshtml`: The partial view that contains the HTML for the grid.
*   `Core/Scripts/KnockoutModels/Common/PracticeInvoices/PracticeInvoiceViewModel.js`: The KnockoutJS view model that provides the data and logic for the grid.

### 9.1. `Details.cshtml`

In `Details.cshtml`, the lab invoices tab is loaded using a partial view:

```html
<div data-bind="visible:  $root.currentInfoView().viewIndex == 1, with: $root.PracticeInvoiceViewModel">
    @Html.Partial("Partials/_Invoices")
</div>
```

### 9.2. `_Invoices.cshtml`

This partial view contains the HTML for the grid and the "Submit" and "Check All" buttons.

```html
<div class="rowContainer">

</div>

<div class="rowContainer">
    <div class="rowContainer_field">
        <input class="rowContainer_button-alt" type="button" data-bind="click: submitInvoices" value="Submit" />
        <input class="rowContainer_button-alt" type="button" data-bind="click: checkAll" value="Check All" id="input-checkall" />
    </div>
</div>


<div id="_invoiceGrid" class="grid" data-bind="kendoGrid: InvoiceGridOptions"></div>
```

#### 9.3.1. `PracticeInvoice.js`

This JavaScript class represents a single lab invoice in the UI.

```javascript
function PracticeInvoice(data) {
    var self = this;

    self.Amount = ko.observable(data.Amount);
    self.DocumentDateTime = ko.observable(data.DocumentDateTime);
    self.InvoiceNumber = ko.observable(data.InvoiceNumber);
    self.IsInQuery = ko.observable(data.IsInQuery);
    self.LabCode = ko.observable(data.LabCode);
    self.LabName = ko.observable(data.LabName);
    self.LedgerEntryId = ko.observable(data.LedgerEntryId);
    self.LineType = ko.observable(data.LineType);
    self.OnHold = ko.observable(data.OnHold);
    self.Performer = ko.observable(data.Performer);
    self.PINumber = ko.observable(data.PINumber);
    self.PostingDate = ko.observable(data.PostingDate);
    self.PracticeNumber = ko.observable(data.PracticeNumber);
    self.Selected = ko.observable(data.Selected);
    self.WorkType = ko.observable(data.WorkType);
}
```

### 9.3.2. `PracticeInvoiceViewModel.js`

This file contains the KnockoutJS view model for the lab invoices grid. It defines the grid columns, data source, and event handlers for the buttons.

```javascript
/**
 * A view model which allows the user to interact with the practice's invoices
 * @param {UserPermissions} user - The current application user
 * @class
 */
function PracticeInvoiceViewModel(user) {

    var self = this;
    var practiceId = $("#PracticeId").val();
    var privateAjaxAction = new AjaxActions(user);
    privateAjaxAction.domain = "";

    self.baseUrl = function () {
        var urlParts = window.location.href.split('/');
        return "/" + urlParts[3] + "/";
    }

    var getInvoicesUrl = self.baseUrl() + "api/LabInvoice";
    var postInvoicsUrl = self.baseUrl() + "LabInvoice/RemoveOnHoldFlag";
    var placeInQueryInvoicsUrl = self.baseUrl() + "LabInvoice/PlaceOnHold";
    self.onHoldFeatureIsEnabled = ko.observable(false);

    ko.bindingHandlers.kendoGrid.options.dataBound = function (data) {

        var head = this.element.find("thead")[0];

        if (head) {
            ko.cleanNode(head);
            ko.applyBindings(ko.dataFor(head), head);

            self.applyCustomZebraClass();
        }
    }

    self.invoices = ko.observableArray([]);

    //Get Invoices
    privateAjaxAction.Get(getInvoicesUrl + '?practiceId=' + practiceId, user)
        .then(function(data) {
            $.each(data,
                function(index, item) {
                    self.invoices.push(new PracticeInvoice(item));
                });
        })
        .fail(function(error) {
            Notify("Error retrieving invoices. Reason: " + error, "error");
        });

    self.onHoldFeatureIsEnabled(true);

    self.checkAll = function (data, event) {
        self.allChecked(!self.allChecked());

        $('#_invoiceGrid > div.k-grid-content > table > tbody > tr').each(function (index, item) {
            if ($(item).find('td:nth-child(12)').text() != 'Y QUERY' &&
                !$(item).find("[type=checkbox][data-type='in query']").prop("checked")) {
                $(item).find("[type=checkbox][data-type='approve']").prop("checked", self.allChecked());
            }
            else if ($(item).find('td:nth-child(12)').text() == 'Y QUERY' &&
                $(item).find("[type=checkbox][data-type='approve']").prop("checked")) {
                $(item).find("[type=checkbox][data-type='approve']").prop("checked", false);
            }
        });

        if (!self.allChecked())
            $('#input-checkall').val('Check All');
        else
            $('#input-checkall').val('Uncheck All');
    };

    self.allChecked = ko.observable();

    self.submitInvoices = function (data, event) {
        var invoicesToApprove = [];
        var invoicesToPutOnHold = [];
        var containsApprovals = false;
        var containsPutInQuery = false;

        $(data.invoices()).each(function (index, invoice) {

            var $row = $('#_invoiceGrid > div.k-grid-content > table > tbody > tr:nth-child(' + (index + 1) + ')');
            var $checkboxApproval = $row.find("[type=checkbox][data-type='approve']");
            var $checkboxOnHold = $row.find("[type=checkbox][data-type='in query']");
            var ledgerId = $row.find('td:nth-child(3)').text();

            if ($checkboxApproval.is(':checked')) {
                invoicesToApprove.push(ledgerId);
                containsApprovals = true;
            }

            if ($checkboxOnHold.is(':checked')) {
                invoicesToPutOnHold.push(ledgerId);
                containsPutInQuery = true;
            }

        }).promise().done(function () {

            if (containsPutInQuery) {
                $.when(privateAjaxAction.Post(placeInQueryInvoicsUrl, JSON.stringify({ ledgerEntryIds: invoicesToPutOnHold }))).then(
                    function (dataRemoveFlag) {
                        if (dataRemoveFlag.IsSuccess) {
                            $(data.invoices()).each(function (index, value) {
                                $row = $('#_invoiceGrid > div.k-grid-content > table > tbody > tr:nth-child(' + (index + 1) + ')');
                                $checkbox = $row.find("[type=checkbox][data-type='in query']");

                                if ($checkbox.is(':checked')) {
                                    value.OnHold('Y QUERY');
                                }
                            });
                            Notify("Successfully put " + invoicesToPutOnHold.length + " Invoices In Query!", "success");
                        }
                        else {
                            $('#div-popup-message').text(dataRemoveFlag.Message.indexOf('No Invoices are there to calculate') != -1 ? "We think all the Invoices selected have been put in query, please refresh the page to see if they're still there." : dataRemoveFlag.Message);
                            $("#div-popup").dialog({
                                modal: true,
                                buttons: {
                                    Ok: function () {
                                        $(this).dialog("close");
                                    }
                                },
                                minWidth: 550,
                                title: "Unfortunately there was an error."
                            });
                        }
                    },
                    function (error) {
                        $('#div-popup-message').text("Please retry and if this keeps happening please contact IT. " + error, "error");
                        $("#div-popup").dialog({
                            modal: true,
                            buttons: {
                                Ok: function () {
                                    $(this).dialog("close");
                                }
                            },
                            minWidth: 550,
                            title: "There has been an error!"
                        });
                    });
            }

            if (containsApprovals) {
                $.when(privateAjaxAction.Post(postInvoicsUrl, JSON.stringify({ ledgerEntryIds: invoicesToApprove }))).then(
                    function (dataRemoveFlag) {
                        if (dataRemoveFlag.IsSuccess) {
                            $(data.invoices()).each(function (index, value) {
                                $row = $('#_invoiceGrid > div.k-grid-content > table > tbody > tr:nth-child(' + (index + 1) + ')');
                                $checkbox = $row.find("[type=checkbox][data-type='approve']");

                                if ($checkbox.is(':checked')) {
                                    self.invoices.remove(value);
                                }
                            });
                            Notify("Successfully Approved " + invoicesToApprove.length + " Invoices!", "success");
                        }
                        else {
                            $('#div-popup-message').text(dataRemoveFlag.Message.indexOf('No Invoices are there to calculate') != -1 ? "We think all the Invoices selected have been approved, please refresh the page to see if they're still there." : dataRemoveFlag.Message);
                            $("#div-popup").dialog({
                                modal: true,
                                buttons: {
                                    Ok: function () {
                                        $(this).dialog("close");
                                    }
                                },
                                minWidth: 550,
                                title: "Unfortunately there was an error."
                            });
                        }
                    },
                    function (error) {
                        $('#div-popup-message').text("Please retry and if this keeps happening please contact IT. " + error, "error");
                        $("#div-popup").dialog({
                            modal: true,
                            buttons: {
                                Ok: function () {
                                    $(this).dialog("close");
                                }
                            },
                            minWidth: 550,
                            title: "There has been an error!"
                        });
                    });
            }

            if (!containsApprovals && !containsPutInQuery) {
                $('#div-popup-message').text("Nothing to approve or put on hold, please select at least 1 Lab Invoice before clicking 'Submit'.");
                $("#div-popup").dialog({
                    modal: true,
                    buttons: {
                        Ok: function () {
                            $(this).dialog("close");
                        }
                    },
                    minWidth: 550,
                    title: "Invoices have not been approved or put on hold!"
                });
            }
        });
    }

    self.applyCustomZebraClass = function () {
        var lastLedgerId = 0;
        var className = 'k-alt';
        $('#_invoiceGrid > div.k-grid-content > table > tbody > tr').each(function (index, item) {
            var ledgerId = $(item).find('td:nth-child(3)').text();

            if (lastLedgerId != ledgerId)
                className = className == 'grid_rowoveride-odd' ? 'grid_rowoveride-even' : 'grid_rowoveride-odd';

            $(item).attr('class', className);

            lastLedgerId = ledgerId;
        });
    }

    self.InvoiceGridOptions = {
        toolbar: ["excel"],
        excel: {
            fileName: "Unapproved Lab Invoices.xlsx",
            filterable: true,
            allPages: true
        },
        data: self.invoices,
        excelExport: function (e) {
            var sheet = e.workbook.sheets[0];
            for (var i = 0; i < sheet.columns.length; i++) {
                sheet.columns[i].width = 150;
            }
        },
        groupable: false,
        resizable: true,
        columns: [
            { title: "Approve", template: "<input type=\"checkbox\" data-type=\"approve\" class=\"checkbox\" onclick=\"PracticeInvoiceViewModel.checkApproveFriends(this);\"/>", width: "6%" },
            { title: "In Query", template: "<input type=\"checkbox\" data-type=\"in query\" class=\"checkbox\" onclick=\"PracticeInvoiceViewModel.checkQueryFriends(this);\"/>", width: "6%" },//hidden: true
            { title: "LedgerEntryId", field: "LedgerEntryId", hidden: true },
            { title: "Lab Name", field: "LabName", width: "15%" },
            { title: "Lab Code", field: "LabCode", width: "6%" },
            { title: "Performer", field: "Performer", width: "15%" },
            { title: "Invoice Number", field: "InvoiceNumber", width: "8%" },
            { title: "PI Number", field: "PINumber", width: "8%" },
            { title: "Work Type", field: "WorkType", width: "4%" },
            { title: "Amount", field: "Amount", format: "{0:n}", width: "6%" },
            { title: "Document Date", field: "DocumentDateTime", width: "8%", type: "date", template: '#= kendo.toString(DocumentDateTime, "d") #' },
            { title: "On Hold", field: "OnHold", width: "5%" },
            { title: "Line Type", field: "LineType", width: "7%" }
        ],
    };
}

PracticeInvoiceViewModel.checkApproveFriends = function (checkBox) {

    var $previousRow = null;

    var $tr = $(checkBox).closest('tr');
    var rowIdentifier = $(checkBox).closest('tr').attr('data-uid');
    var selectedIndex = 0;
    var rowIndex = 0;
    var isChecked = false;
    var foundMatch = false;
    var ledgerId = null;

    $('#_invoiceGrid > div.k-grid-content > table > tbody > tr').each(function (index, target) {
        if ($(target).attr('data-uid') === rowIdentifier) {
            isChecked = $(checkBox).is(':checked');
            ledgerId = $tr.find('td:nth-child(3)').text();
            foundMatch = true;
            return false;
        }
    });

    //check for any previous approvals
    $('#_invoiceGrid > div.k-grid-content > table > tbody > tr').each(function (index, target) {
        if ($(target).find('td:nth-child(3)').text() === ledgerId) {
            $(target).find("[type=checkbox][data-type='approve']").prop("checked", isChecked);
        }
    });

    //Untick all approve flags and friends
    if (isChecked) {
        $('#_invoiceGrid > div.k-grid-content > table > tbody > tr').each(function (index, target) {
            if ($(target).find('td:nth-child(3)').text() === ledgerId) {
                $(target).find("[type=checkbox][data-type='in query']").prop("checked", false);

            }
        });
    }
}

PracticeInvoiceViewModel.checkQueryFriends = function (checkBox) {

    var $previousRow = null;

    var $tr = $(checkBox).closest('tr');
    var rowIdentifier = $(checkBox).closest('tr').attr('data-uid');
    var selectedIndex = 0;
    var rowIndex = 0;
    var isChecked = false;
    var foundMatch = false;
    var ledgerId = null;

    $('#_invoiceGrid > div.k-grid-content > table > tbody > tr').each(function (index, target) {

        if ($(target).attr('data-uid') === rowIdentifier) {
            isChecked = $(checkBox).is(':checked');
            ledgerId = $tr.find('td:nth-child(3)').text();
            foundMatch = true;
            return false;
        }
    });

    //check for any previous approvals
    $('#_invoiceGrid > div.k-grid-content > table > tbody > tr').each(function (index, target) {
        if ($(target).find('td:nth-child(3)').text() === ledgerId) {
            $(target).find("[type=checkbox][data-type='in query']").prop("checked", isChecked);
        }
    });

    //Untick all approve flags and friends
    if (isChecked) {
        $('#_invoiceGrid > div.k-grid-content > table > tbody > tr').each(function (index, target) {
            if ($(target).find('td:nth-child(3)').text() === ledgerId) {
                $(target).find("[type=checkbox][data-type='approve']").prop("checked", false);

            }
        });
    }
}
```

### 9.4. Static Assets

The following static assets (images) are referenced in the UI and should be included in the new project:

*   `~/Core/Images/Clipboard.png`
*   `~/Core/Images/BankNote.png`
*   `~/Core/Images/loading.gif`

### 9.5. Final Result

The final result should look like the following image:

![Lab Invoices](@LabInvoices.png)

## 10. Permissions

The permissions for this functionality are controlled by the `LabInvoiceModel.HasPermission` method. The following roles have access:

*   **ADM (Area Dental Manager):** Users with the ADM role have full access.
*   **Clarity Super User:** Users with the Clarity Super User role have full access.
*   **PM (Practice Manager):** Users with the PM role can only access the functionality for their assigned practice. The `practiceIdToBeAuthorised` must match the value returned by `currentUser.GetLocationForLabInvoices()`.

Additionally, the `LabInvoiceController` checks `CurrentUser.GetIsOverridden()`. This method is part of the `WisdomUser` class (from `IDHGroup.SharedLibraries.WisdomUsers`) and indicates if the current user's identity has been overridden.

## 11. Missing Classes and Methods

This section details additional classes and methods that are used by the lab invoice functionality and need to be included in the new project.

### 11.1. `LabInvoiceMissingException.cs`

This custom exception is thrown when a lab invoice is not found.

```csharp
using System;

namespace IDHGroup.UI.PracticeView.Helpers
{
    public class LabInvoiceMissingException : Exception
    {
        public LabInvoiceMissingException(string message)
            : base(message)
        {
        }

        public LabInvoiceMissingException(int ledgerEntryId) : base (string.Format("LedgerEntryId {0} has already been approved", ledgerEntryId))
        { 
        }

    }
}
```

### 11.2. `GetUnapprovedCountRequest.cs`

This class is used as a request model for the `GetUnapprovedCount` API endpoint.

```csharp
using System.Collections.Generic;

namespace IDHGroup.UI.PracticeView.Controllers.API
{
    public class GetUnapprovedCountRequest
    {
        public List<string> Practices;
        public string PracticeQuery;
    }
}
```

### 11.3. `DataRowExtension.cs`

This extension class provides a helper method for safely retrieving column values from a `DataRow`.

```csharp
using System;
using System.Data;
using System.Linq;

namespace IDHGroup.UI.PracticeView.Helpers.Extensions
{
    public static class DataRowExtension
    {
        /// <summary>
        /// Returns DBNull if the datarow does not contain a value in the specified column or if the specified column does not exist in the datarow. Otherwise, returns the value stored in the column.
        /// </summary>
        /// <param name="dataRow">The datarow which will will retrieve a value from</param>
        /// <param name="columnName">The name of the column which is holding our value</param>
        /// <returns></returns>
        public static bool HasColumnValue(this DataRow dataRow, string columnName)
        {
            return dataRow.Table.Columns.Contains(columnName) && dataRow[columnName] != DBNull.Value;
        }

        public static T GetColumnValueOrDefault<T>(this DataRow dataRow, string columnName)
        {
            return GetColumnValueOrDefault<T>(dataRow, columnName, default(T));
        }

        public static T GetColumnValueOrDefault<T>(this DataRow dataRow, string columnName, T defaultValue)
        {
            if (!dataRow.HasColumnValue(columnName))
                return defaultValue;

            return (T)dataRow.GetColumnValue(columnName, typeof(T));
        }

        private static object GetColumnValue(this DataRow dataRow, string columnName, Type columnType)
        {
            // Column value will always be a primitive type. Get the code so we can use the right convert function for T's type
            TypeCode columnTypeCode = Type.GetTypeCode(columnType);

            switch (columnTypeCode)
            {
                case TypeCode.Boolean:
                    return Convert.ToBoolean(dataRow[columnName]);
                case TypeCode.Byte:
                    return Convert.ToInt32(dataRow[columnName]);
                case TypeCode.Char:
                    return Convert.ToChar(dataRow[columnName]);
                case TypeCode.Decimal:
                    return Convert.ToDecimal(dataRow[columnName]);
                case TypeCode.DateTime:
                    return Convert.ToDateTime(dataRow[columnName]);
                case TypeCode.Double:
                    return Convert.ToDouble(dataRow[columnName]);
                case TypeCode.Int16:
                    return Convert.ToInt16(dataRow[columnName]);
                case TypeCode.Int32:
                    return Convert.ToInt32(dataRow[columnName]);
                case TypeCode.Int64:
                    return Convert.ToInt64(dataRow[columnName]);
                case TypeCode.UInt16:
                    return Convert.ToUInt16(dataRow[columnName]);
                case TypeCode.UInt32:
                    return Convert.ToUInt32(dataRow[columnName]);
                case TypeCode.UInt64:
                    return Convert.ToUInt64(dataRow[columnName]);
                case TypeCode.SByte:
                    return Convert.ToSByte(dataRow[columnName]);
                case TypeCode.Single:
                    return Convert.ToSingle(dataRow[columnName]);
                case TypeCode.String:
                    return Convert.ToString(dataRow[columnName]);
                case TypeCode.Object:
                    return dataRow.GetImplementedColumnObject(columnName, columnType);
                default:
                    throw new NotImplementedException(string.Format(_exceptionMessage, columnType.FullName));
            }
        }

        private static object GetImplementedColumnObject(this DataRow dataRow, string columnName, Type columnType)
        {
            // DataRows support timespans so we should return these.
            if (columnType == typeof(TimeSpan) || columnType == typeof(TimeSpan?))
                return (TimeSpan)dataRow[columnName];

            if (columnType == typeof(Guid) || columnType == typeof(Guid?))
                return (Guid) dataRow[columnName];

            if (columnType == typeof(byte[]))
            {
                return (byte[]) dataRow[columnName];
            }

            Type columnBaseType = columnType.GenericTypeArguments.FirstOrDefault();

            // In case T is of type Object and not a subclass of Object, throw an exception
            if (columnBaseType == null)
                throw new NotImplementedException(string.Format(_exceptionMessage, columnType.FullName));

            // Otherwise, try to return the object from the type's subclasses
            return dataRow.GetColumnValue(columnName, columnBaseType);
        }

        private static string _exceptionMessage = "Cannot retrieve column value for {0}";
    }
}
```