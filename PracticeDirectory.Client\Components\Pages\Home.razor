﻿@page "/"
@using System.Net.Http.Headers
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.Extensions.Options
@using PracticeDirectory.Client.Shared.Configuration
@using PracticeDirectory.Client.Shared.Extensions
@using PracticeDirectory.Client.Shared.Models
@using PracticeDirectory.Client.Shared.Models.Practices
@using PracticeDirectory.Client.Service
@inject IHttpClientFactory ClientFactory
@inject UserContextService UserContext
@inject IOptions<AdminTeamSettings> AdminTeamConfig
@inject IJSRuntime JSRuntime

<style>
    .custom-header-wrapper {
        width: 100%;
        display: flex;
        justify-content: center;
        margin-bottom: 2rem;
    }

    .practice-heading {
        font-size: 2rem; /* Adjust for balance */
        font-weight: 700;
        color: #212529; /* Equivalent to Bootstrap text-dark */
        margin: 0;
        white-space: nowrap;
    }

    body {
        font-family: 'Roboto', sans-serif;
        background-color: #f4f7fa;
        margin: 0;
        padding: 0;
        color: #333;
    }

    h1 {
        font-size: 2.00rem;
        color: #2c3e50;
        word-wrap: break-word;
        max-width: 90%;
        margin: 0 auto;
    }

    .text-teal {
        color: #008080;
        text-decoration: none;
        transition: color 0.3s ease, text-decoration 0.3s ease;
    }

        .text-teal:hover {
            color: #004d40;
            text-decoration: underline;
        }

    .card {
        border-radius: 12px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        transition: box-shadow 0.3s ease, transform 0.3s ease;
        background-color: white;
    }

        .card:hover {
            box-shadow: 0 6px 30px rgba(0, 0, 0, 0.1);
            transform: translateY(-5px);
        }

    .card-header {
        background-color: #f1f3f5;
        border-bottom: 2px solid #e0e0e0;
        border-top-left-radius: 12px;
        border-top-right-radius: 12px;
        padding: 12px;
    }


    .form-control {
        border-radius: 8px;
        padding: 12px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
    }

        .form-control:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.25rem rgba(0, 123, 255, 0.25);
        }

    .form-label {
        font-weight: 600;
        color: #34495e;
    }


    .btn-primary, .btn-outline-primary {
        border-radius: 10px;
        padding: 8px 20px;
        font-size: 14px;
        transition: background-color 0.3s ease, transform 0.3s ease, color 0.3s ease;
    }

    .btn-primary {
        background-color: #007bff;
        border: none;
        color: white;
    }

    .btn-outline-primary {
        border-color: #007bff;
        color: #007bff;
    }

        .btn-primary:hover, .btn-outline-primary:hover {
            background-color: #0056b3;
            color: #003167;
            transform: translateY(-3px);
        }

        .btn-outline-primary:hover {
            background-color: #e7f1fb;
        }


    .spinner-grow {
        width: 3rem;
        height: 3rem;
        color: #007bff;
    }

    .spinner-grow-container {
        display: flex;
        justify-content: center;
        gap: 15px;
        margin-top: 30px;
    }


    .alert {
        border-radius: 10px;
        padding: 15px;
        font-size: 1rem;
    }


    .input-group-text {
        background-color: #007bff;
        color: white;
        border-radius: 8px;
        padding: 10px;
    }

        .input-group-text i {
            font-size: 1.1rem;
        }


    .container-fluid {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }


    .override-container {
        max-width: 600px;
        margin: 0 auto;
    }

    .override-notice {
        max-width: 600px;
        margin: 0 auto;
        padding: 15px;
        border-radius: 8px;
    }

        .override-notice h5 {
            font-size: 1.1rem;
            margin-bottom: 10px;
        }

        .override-notice .fw-semibold {
            font-size: 1.2rem;
            font-weight: 600;
        }


        .override-notice .btn {
            padding: 6px 12px;
            font-size: 0.9rem;
        }

</style>

<PageTitle>Home</PageTitle>

<div class="custom-header-wrapper">
    <h1 class="practice-heading">Practice Directory</h1>
</div>

@if (IsAdmin)
{
    @if (isOverridden)
    {
        <div class="override-notice card p-3 mb-4 border-start border-4 border-primary bg-light">
            <h5 class="text-primary mb-2">Currently overriding as:</h5>
            <div class="fw-semibold text-dark">@overrideUsername</div>
            <button class="btn btn-outline-primary btn-sm mt-3" @onclick="ClearOverride">
                <i class="bi bi-x-circle me-1"></i> Clear Override
            </button>
        </div>
    }

    <div class="mb-4 override-container">
        <label for="overrideInput" class="form-label">Override as User</label>
        <input id="overrideInput"
               type="text"
               class="form-control"
               placeholder="Enter username to impersonate"
               @bind="overrideUsername"
               @bind:after="OnOverrideChanged"
               aria-label="Username override input" />
    </div>
}

<div class="container-fluid py-4">
    <div class="row justify-content-between align-items-end mb-4">
        <div class="col-md-6 col-lg-4">
            <label for="searchText" class="form-label">Practice Name or Number</label>
            <div class="input-group">
                <input id="searchText"
                       type="text"
                       class="form-control shadow-sm"
                       @onchange="CallUrl"
                       placeholder="Search for a practice..."
                       aria-label="Search input for practice name or number" />
                <span class="input-group-text">
                    <i class="bi bi-search" @onclick='() => CallUrlSearch()'></i>
                </span>
            </div>
        </div>
    </div>

    @if (practiceData?.Records?.Any() == true)
    {
        @foreach (var practice in practiceData.Records)
        {
            <div class="card mb-3">
                <div class="card-body d-flex justify-content-between align-items-center">
                    <a href="practice/@practice.Id" class="text-teal fw-semibold mb-0" title="View details of @practice.WebsiteName">
                        @practice.Id – @practice.WebsiteName
                    </a>
                    <a href="practice/@practice.Id" class="btn btn-sm btn-outline-primary" title="Go to practice details page">View Practice</a>
                </div>
            </div>
        }
    }
    else
    {
        @if (showNoResultsFound)
        {
            <div class="alert alert-info mt-3" role="alert">
                No results found. Try searching with a practice name or number.
            </div>
        }
    }
</div>

@if (showSpinner)
{
    <div class="spinner-grow-container">
        <div class="spinner-grow text-muted" role="status"></div>
        <div class="spinner-grow text-muted" role="status"></div>
        <div class="spinner-grow text-muted" role="status"></div>
    </div>
}



@code {
    private DirectoryPracticeMetaData practiceData = new();
    private string overrideUsername = string.Empty;
    private string actualUser = string.Empty;
    private string userName = string.Empty;
    private string Originaluser = string.Empty;
    private string isAuthenticated = string.Empty;
    private string searchText = string.Empty;
    private bool showSpinner = false;
    private bool showNoResultsFound = false;
    private bool IsAdmin = false;
    private bool isOverridden => !string.IsNullOrWhiteSpace(overrideUsername);

    [CascadingParameter]
    private Task<AuthenticationState> authenticationStateTask { get; set; } = default!;

    protected override async Task OnInitializedAsync()
    {
        // Load override from localStorage
        var storedOverride = await JSRuntime.InvokeAsync<string>("localStorage.getItem", "overrideUsername");
        UserContext.OverrideUsername = storedOverride ?? string.Empty;
        overrideUsername = UserContext.OverrideUsername;

        // Auth setup
        var authState = await authenticationStateTask;
        actualUser = authState.GetWindowsUserName(UserContext.OverrideUsername);
        userName = string.IsNullOrWhiteSpace(overrideUsername) ? actualUser : overrideUsername;
        isAuthenticated = authState.GetWindowsUserIsAuthenticated().ToYesNo();
        Originaluser = authState.GetOriginalWindowsUserName();
        IsAdmin = IsUserAdmin(Originaluser);

        if (!IsAdmin) { await ClearOverride(); }
        await JSRuntime.InvokeVoidAsync("localStorage.removeItem", "practiceId");
    }

    private bool IsUserAdmin(string user)
    {
        return AdminTeamConfig.Value
            .GetUserList()
            .Contains(user.Split('\\').Last(), StringComparer.OrdinalIgnoreCase);
    }

    private async Task OnOverrideChanged()
    {
        UserContext.OverrideUsername = overrideUsername;
        await JSRuntime.InvokeVoidAsync("localStorage.setItem", "overrideUsername", overrideUsername);
    }

    private async Task ClearOverride()
    {
        overrideUsername = string.Empty;
        UserContext.OverrideUsername = string.Empty;
        await JSRuntime.InvokeVoidAsync("localStorage.removeItem", "overrideUsername");
    }

    private async Task CallUrl(ChangeEventArgs e)
    {
        string searchTerm = e.Value?.ToString() ?? string.Empty;
        searchText = searchTerm;
        await CallUrlSearch();
    }

    private async Task CallUrlSearch()
    {
        try
        {
            showNoResultsFound = false;
            showSpinner = true;

            if (string.IsNullOrWhiteSpace(searchText))
            {
                showNoResultsFound = true;
            }
            else
            {
                var httpClient = ClientFactory.CreateClient(ApiSettings.Api);
                httpClient.DefaultRequestHeaders.Add("X-ORIG-NAME", userName);

                var response = await httpClient.PostAsync($"directorypractice/search?searchTerm={searchText}", null);
                var result = await response.Content.ReadFromJsonAsync<DirectoryPracticeMetaData>();

                practiceData = result ?? new();
                if (result?.Records.Count() == 0)
                {
                    showNoResultsFound = true;
                }
            }
        }
        finally
        {
            showSpinner = false;
        }
    }
}
