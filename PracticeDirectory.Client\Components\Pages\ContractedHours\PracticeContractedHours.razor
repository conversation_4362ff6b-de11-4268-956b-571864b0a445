﻿@using Microsoft.AspNetCore.Components.Authorization
@using PracticeDirectory.Client.Service
@using PracticeDirectory.Client.Shared.Configuration
@using PracticeDirectory.Client.Shared.Extensions
@using PracticeDirectory.Client.Shared.Models.Practices
@inject IHttpClientFactory ClientFactory
@inject UserContextService UserContext

<div class="row">
    @if (contractedHours.Any())
    {
        for (int i = 1; i < 8; i++)
        {
            var dayModel = contractedHours.FirstOrDefault(c => c.Day_Of_Week == i);

            <div class="col openinghourscard openinghoursprofile-card p-3 px-4 shadow-sm rounded-3 text-center" style="margin-right: 1.5rem; margin-top: 1.5rem;">
                <div>
                    <strong>@days[i]</strong>
                </div>
                <div class="mt-2">
                    @if (dayModel != null)
                    {
                        if (dayModel.Is_Closed == 1)
                        {
                            <strong><span>Closed</span></strong>
                        }
                        else
                        {
                            <strong><span>@dayModel.Opening_Time_Name - @dayModel.Closing_Time_Name</span></strong>
                        }
                    }
                    else
                    {
                        <strong>-</strong>
                    }
                </div>
            </div>
        }
    }
    <div class="row mt-3">
        @if (showInProgressWarningMessage)
        {
            <div class="alert alert-warning" role="alert">
                Requested Contracted Hours changes are in progress. This can take up to 24 hours.
            </div>
        }
    </div>
</div>
<div class="d-flex flex-wrap gap-3 mt-4">
    @if (isAdmin || IsCommissioningTeamUser)
		{
    <a href="@($"practice/editContractedHours/{PracticeId}")" class="btn btn-primary col-12 col-md-auto">Edit Contracted Hours</a>
        }
</div>

<div class="row mt-4">
    @if (showErrorMessage)
    {
        <div class="alert alert-danger" role="alert">
            Contracted Hours Not Found
        </div>
    }
</div>

@code {
    [Parameter]
    public int PracticeId { get; set; }
    private IEnumerable<PracticeContractedHoursDto> contractedHours = [];
    private string userName = string.Empty;
    private bool showInProgressWarningMessage = false;
    private bool isAdmin = false;
    private bool IsCommissioningTeamUser = false;

    private string[] days { get; } = ["", "MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY", "SATURDAY", "SUNDAY"];
    private bool showErrorMessage = false;

    [CascadingParameter]
    private Task<AuthenticationState> authenticationStateTask { get; set; } = default!;

    protected override async Task OnInitializedAsync()
    {
        var authState = await authenticationStateTask;
        userName = authState.GetWindowsUserName(UserContext.OverrideUsername);
        await GetContractedHoursAsync();
        isAdmin = AuthExtensions.IsUserAdmin(authState.GetOriginalWindowsUserName());
        IsCommissioningTeamUser = authState.CheckCurrentUserIsInCommissioningTeam();
    }


    private async Task GetContractedHoursAsync()
    {
        try
        {
            var httpClient = ClientFactory.CreateClient("Api");
            httpClient.DefaultRequestHeaders.Add("X-ORIG-NAME", userName);

            var response = await httpClient.GetAsync($"practicecontractedhours/{PracticeId}");
            response.EnsureSuccessStatusCode();

            var apiResult = await response.Content.ReadFromJsonAsync<ApiResponse<IEnumerable<PracticeContractedHoursDto>>>();
            contractedHours = apiResult?.Records ?? new List<PracticeContractedHoursDto>();
            if (contractedHours.Count() > 0)
            {
                var firstItem = contractedHours.FirstOrDefault();
                if (firstItem?.In_Progress == true)
                {
                    showInProgressWarningMessage = true;
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading contracted hours: {ex.Message}.");
            contractedHours = new List<PracticeContractedHoursDto>();
            showErrorMessage = true;
        }
    }
    private class ApiResponse<T>
    {
        public T? Records { get; set; }
    }

}