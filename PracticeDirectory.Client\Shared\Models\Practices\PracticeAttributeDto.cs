﻿namespace PracticeDirectory.Client.Shared.Models.Practices
{
    using System.Text.Json.Serialization;
    public class PracticeAttributeDto
    {
        public int PracticeId { get; set; }
        public string CustomAttributeCode { get; set; } = string.Empty;
        public string CustomAttributeName { get; set; } = string.Empty;
        public string CustomAttributeTypeName { get; set; } = string.Empty;
        public object? Value { get; set; }
        public string Notes { get; set; } = string.Empty;

        [JsonIgnore]
        public bool IsYes
        {
            get => Value?.ToString() == "Y";
            set => Value = value ? "Y" : "N";
        }

        [JsonIgnore]
        public decimal? NumericValue
        {
            get => Value is null ? null : (decimal.TryParse(Value.ToString(), out var result) ? result : null);
            set => Value = value;
        }

        // SOLUTION: Add a new proxy property for Text/String types.
        // This avoids casting in the .razor file and makes binding safe.
        [JsonIgnore]
        public string? StringValue
        {
            get => Value?.ToString();
            set => Value = value;
        }
    }

    public class PracticeAttributeResponse
    {
        public List<PracticeAttributeDto> Records { get; set; } = new();
    }

}
