.profile-image {
    max-width: 100%;
    height: auto;
    object-fit: contain;
    max-height: 400px; /* Increased max height */
}

/* Specific styling for the image preview in the upload modal */
.image-preview-container {
    max-height: 300px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 1rem 0;
}

.image-preview {
    max-width: 100%;
    max-height: 280px;
    width: auto;
    height: auto;
    object-fit: contain;
}

.image-placeholder {
    width: 100%;
    height: 200px;
    background-color: #f8f9fa;
    border: 2px dashed #dee2e6;
    border-radius: 0.25rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.image-placeholder span {
    color: #6c757d;
    font-size: 1rem;
}

/* Thumbnail gallery styles */
.thumbnail-gallery {
    margin-bottom: 1rem;
}

/* Base styles for all thumbnails */
.thumbnail-container {
    padding: 0.25rem;
    cursor: pointer;
    transition: all 0.2s ease-in-out;
    position: relative;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 0.25rem;
}

.thumbnail-container:hover {
    transform: scale(1.05);
}

/* Override any Bootstrap styling */
.img-thumbnail {
    border-color: transparent !important;
    box-shadow: none !important;
}

.thumbnail-image {
    max-height: 80px;
    width: auto;
    margin: 0 auto;
    border: 2px solid transparent;
    object-fit: contain;
    border-radius: 0.25rem;
}

/* Only the selected thumbnail gets styling */
.thumbnail-container.selected {
    background-color: rgba(13, 110, 253, 0.1); /* Light blue background */
}

.thumbnail-container.selected .thumbnail-image {
    border-color: #0d6efd;
    box-shadow: 0 0 0 2px #0d6efd;
}

/* Selected indicator - a small dot in the corner */
.selected-indicator {
    position: absolute;
    top: 5px;
    right: 5px;
    width: 12px;
    height: 12px;
    background-color: #0d6efd;
    border-radius: 50%;
    border: 2px solid white;
    z-index: 10; /* Ensure it's above other elements */
}
