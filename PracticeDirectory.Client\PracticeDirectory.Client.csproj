﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Havit.Blazor.Components.Web.Bootstrap" Version="4.8.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.Negotiate" Version="9.0.4" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="wwwroot\Images\" />
  </ItemGroup>

</Project>
