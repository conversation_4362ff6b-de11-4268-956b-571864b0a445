@using System.Text.Json
@using PracticeDirectory.Client.Service
@inject IFileStorageHubService FileStorageHubService

<div class="@CssClass">
    @if (IsLoading)
    {
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
    }
    else if (!string.IsNullOrEmpty(ImageDataUrl))
    {
        <img src="@ImageDataUrl" alt="@Alt" class="@ImageCssClass" />
    }
    else if (!string.IsNullOrEmpty(ErrorMessage))
    {
        <div class="alert alert-danger">
            @ErrorMessage
        </div>
    }
    else
    {
        <div class="image-placeholder">
            <span>Image not available</span>
        </div>
    }
</div>

@code {
    [Parameter]
    public string ImagePath { get; set; } = string.Empty;

    [Parameter]
    public string Alt { get; set; } = "Image";

    [Parameter]
    public string CssClass { get; set; } = "text-center";

    [Parameter]
    public string ImageCssClass { get; set; } = "img-fluid rounded";

    private string ImageDataUrl { get; set; } = string.Empty;
    private string ErrorMessage { get; set; } = string.Empty;
    private bool IsLoading { get; set; } = true;

    protected override async Task OnParametersSetAsync()
    {
        if (string.IsNullOrEmpty(ImagePath))
        {
            IsLoading = false;
            return;
        }

        try
        {
            IsLoading = true;
            ErrorMessage = string.Empty;

            // If the image path is already a data URL, use it directly
            if (ImagePath.StartsWith("data:image"))
            {
                ImageDataUrl = ImagePath;
                IsLoading = false;
                return;
            }

            // Use the GetImageDataUrlAsync method which handles caching
            ImageDataUrl = await FileStorageHubService.GetImageDataUrlAsync(ImagePath);
        }
        catch (Exception ex)
        {
            ErrorMessage = $"Error loading image: {ex.Message}";
            Console.WriteLine($"Error loading image: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
        }
        finally
        {
            IsLoading = false;
        }
    }

    public class FileStorageHubResponse
    {
        [System.Text.Json.Serialization.JsonPropertyName("fileName")]
        public string FileName { get; set; } = string.Empty;

        [System.Text.Json.Serialization.JsonPropertyName("fileExtension")]
        public string FileExtension { get; set; } = string.Empty;

        [System.Text.Json.Serialization.JsonPropertyName("fileBytes")]
        public string FileBytes { get; set; } = string.Empty;
    }

    private static readonly JsonSerializerOptions _jsonOptions = new JsonSerializerOptions
    {
        PropertyNameCaseInsensitive = true
    };
}
