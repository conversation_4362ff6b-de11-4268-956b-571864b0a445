namespace PracticeDirectory.Client.Shared.Models;

public class PublicProfileImageModel
{
    public int Id { get; set; }
    public string PersonId { get; set; } = string.Empty;
    public string ImageUrl { get; set; } = string.Empty;
    public int ApprovalStatusId { get; set; }
    public string? ApprovalStatus { get; set; } // Added string approval status
    public string? ImageType { get; set; } // Added image type
    public string? Notes { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
    public DateTime CreatedDate { get; set; }
    public string? UpdatedBy { get; set; }
    public DateTime? UpdatedDate { get; set; }
}

public class PublicProfileImageSearchRequest
{
    public string PersonId { get; set; } = string.Empty;
    // Using strings for approval status as specified
    public List<string>? ApprovalStatus { get; set; } = new List<string>();
}

public class PublicProfileImageTypeModel
{
    public string Name { get; set; } = string.Empty;
    public string Code { get; set; } = string.Empty;
}

public class PublicProfileImageTypeResponse
{
    public List<PublicProfileImageTypeModel> Records { get; set; } = new List<PublicProfileImageTypeModel>();
    public MetadataModel Metadata { get; set; } = new MetadataModel();
}

public class MetadataModel
{
    public int TotalCount { get; set; }
}

public class PublicProfileImageSearchResponse
{
    public List<PublicProfileImageModel> Records { get; set; } = [];
}
