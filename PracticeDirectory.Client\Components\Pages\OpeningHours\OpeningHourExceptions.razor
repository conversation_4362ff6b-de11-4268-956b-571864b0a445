﻿@using Havit.Blazor.Components.Web.Bootstrap
@using Havit.Blazor.Components.Web.Bootstrap.Forms
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Components.Forms
@using PracticeDirectory.Client.Service
@using PracticeDirectory.Client.Shared.Models.Practices
@using PracticeDirectory.Client.Shared.Extensions
@using System.ComponentModel.DataAnnotations
@using System.Net.Http
@inject IHttpClientFactory ClientFactory
@inject UserContextService UserContext
@inject AuthenticationStateProvider AuthenticationStateProvider

@if (isLoading)
{
    <p><em>Loading exceptions...</em></p>
}
else
{
    @if (CanEdit)
    {
        <div class="row mb-4">
            <div class="col">
                <HxButton Text="Add New Exception" Color="ThemeColor.Primary" OnClick="HandleAddNewException" Icon="BootstrapIcon.PlusCircle" />
            </div>
        </div>
    }

    @if (showGeneralSuccessMessage)
    {
        <div class="alert alert-success">@generalMessage</div>
    }
    @if (showGeneralErrorMessage)
    {
        <div class="alert alert-danger">@generalMessage</div>
    }
    @if (showApiWarningMessage)
    {
        <div class="alert alert-warning">@generalMessage</div>
    }

    @if (!exceptions.Any() && !showGeneralSuccessMessage && !showGeneralErrorMessage && !showApiWarningMessage)
    {
        <div class="alert alert-info" role="alert">
            There are no opening hour exceptions scheduled.
        </div>
    }
    else if (exceptions.Any())
    {
        <div class="list-group">
            @foreach (var exception in exceptions.OrderBy(e => e.StartDate))
            {
                <div class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="mb-1">@exception.Description</h5>
                        <p class="mb-1 text-muted">
                            <i class="oi oi-calendar" aria-hidden="true"></i>
                            @exception.DateRangeDisplay
                        </p>
                    </div>
                    @if(CanEdit)
                    {
                        <div>
                            <HxButton Color="ThemeColor.Secondary" OnClick="() => HandleEditException(exception)" Icon="BootstrapIcon.Pencil" CssClass="me-2" />
                            <HxButton Color="ThemeColor.Danger" OnClick="() => HandleDeleteException(exception)" Icon="BootstrapIcon.Trash" />
                        </div>
                    }
                </div>
            }
        </div>
    }
}

<HxModal @ref="addEditModal" Title="@modalTitle">
    <BodyTemplate>
        <EditForm EditContext="editContext" OnValidSubmit="HandleValidSubmit">
            <DataAnnotationsValidator />

            @if (!string.IsNullOrEmpty(modalErrorMessage))
            {
                <div class="alert alert-danger">@modalErrorMessage</div>
            }


            <div class="mb-3">
                <HxInputText @bind-Value="currentException.Description" Label="Description (e.g., Christmas Break, Staff Training)" />
            </div>
            <div class="row">
                <div class="col-md-6 mb-3">
                    <HxInputDate TValue="DateTime?" @bind-Value="currentException.StartDate" Label="Start Date" />
                </div>
                <div class="col-md-6 mb-3">
                    <HxInputDate TValue="DateTime?" @bind-Value="currentException.EndDate" Label="End Date" />
                </div>
            </div>
        </EditForm>
    </BodyTemplate>
    <FooterTemplate>
        <HxButton Text="Cancel" Color="ThemeColor.Secondary" OnClick="HandleCancel" />
        <HxButton Text="Save" Color="ThemeColor.Primary" OnClick="HandleValidSubmit" />
    </FooterTemplate>
</HxModal>

<HxModal @ref="deleteConfirmModal" Title="Confirm Deletion">
    <BodyTemplate>
        @if (!string.IsNullOrEmpty(modalErrorMessage))
        {
            <div class="alert alert-danger">@modalErrorMessage</div>
        }
        @if(currentException != null)
        {
            <p>Are you sure you want to delete the exception period for <strong>@currentException.Description</strong> (@currentException.DateRangeDisplay)?</p>
            <p class="text-danger">This action cannot be undone.</p>
        }
    </BodyTemplate>
    <FooterTemplate>
        <HxButton Text="Cancel" Color="ThemeColor.Secondary" OnClick="HandleCancel" />
        <HxButton Text="Delete" Color="ThemeColor.Danger" OnClick="HandleConfirmDelete" />
    </FooterTemplate>
</HxModal>

@code {
    [Parameter]
    public int PracticeId { get; set; }

    [Parameter]
    public bool CanEdit { get; set; }
    [CascadingParameter]
    private Task<AuthenticationState> authenticationStateTask { get; set; } = default!;
    private string isAuthenticated = string.Empty;
    private bool isPracticeManager { get; set; } = false;
    private int currentUserPracticeId { get; set; } = 0;
    private string originalUser = string.Empty;
    private bool isAdmin = false;

    private List<PracticeOpeningHoursExceptionModel> exceptions = new();
    private PracticeOpeningHoursExceptionModel currentException = new();
    private bool isLoading = true;
    private string modalTitle = string.Empty;
    private string userName = string.Empty;

    private EditContext editContext = default!;

    private string? modalErrorMessage;
    private bool showGeneralSuccessMessage;
    private bool showGeneralErrorMessage;
    private bool showApiWarningMessage;
    private string? generalMessage;

    private HxModal addEditModal = null!;
    private HxModal deleteConfirmModal = null!;

    protected override async Task OnInitializedAsync()
    {   
        var authState = await authenticationStateTask;
        userName = authState.GetWindowsUserName(UserContext.OverrideUsername);
        isAuthenticated = authState.GetWindowsUserIsAuthenticated().ToYesNo();
        currentUserPracticeId = authState.GetPracticeId(ClientFactory, UserContext.OverrideUsername);
        isPracticeManager = await GetCurrentPMAsync(PracticeId, currentUserPracticeId);
        originalUser = authState.GetOriginalWindowsUserName();
        isAdmin = AuthExtensions.IsUserAdmin(originalUser);

        editContext = new EditContext(new PracticeOpeningHoursExceptionModel());
        await LoadExceptions();
        isLoading = false;
    }    
    private async Task<bool> GetCurrentPMAsync(int ParentPractice, int CurrentPractice)
    {
        if (ParentPractice != 0 && CurrentPractice != 0)
        {
            return ParentPractice == CurrentPractice;
        }

        await Task.CompletedTask;
        return false;
    }

    private async Task LoadExceptions()
    {
        ClearGeneralMessages();
        try
        {
            var httpClient = ClientFactory.CreateClient("Api");
            httpClient.DefaultRequestHeaders.Add("X-ORIG-NAME", userName);

            var response = await httpClient.GetAsync($"practiceopeninghoursexceptions/practice/{PracticeId}");

            response.EnsureSuccessStatusCode();

            var result = await response.Content.ReadFromJsonAsync<ApiResponse<List<PracticeOpeningHoursExceptionModel>>>();
            exceptions = result?.Records ?? new List<PracticeOpeningHoursExceptionModel>();
        }
        catch (Exception ex)
        {
            generalMessage = "Could not load opening hour exceptions at this time.";
            showGeneralErrorMessage = true;
            Console.WriteLine($"Error loading exceptions from API: {ex.Message}.");
            exceptions = new List<PracticeOpeningHoursExceptionModel>();
        }
    }

    private void HandleAddNewException()
    {
        modalTitle = "Add New Exception";
        currentException = new PracticeOpeningHoursExceptionModel
        {
            PracticeId = this.PracticeId,
            StartDate = DateTime.Today,
            EndDate = DateTime.Today
        };
        editContext = new EditContext(currentException);
        _ = InvokeAsync(addEditModal.ShowAsync);
    }

    private void HandleEditException(PracticeOpeningHoursExceptionModel exceptionToEdit)
    {
        modalTitle = "Edit Exception";
        currentException = new PracticeOpeningHoursExceptionModel
        {
            Id = exceptionToEdit.Id,
            PracticeId = exceptionToEdit.PracticeId,
            Description = exceptionToEdit.Description,
            StartDate = exceptionToEdit.StartDate,
            EndDate = exceptionToEdit.EndDate
        };
        editContext = new EditContext(currentException);
        _ = InvokeAsync(addEditModal.ShowAsync);
    }

    private void HandleDeleteException(PracticeOpeningHoursExceptionModel exceptionToDelete)
    {
        currentException = exceptionToDelete;
        _ = InvokeAsync(deleteConfirmModal.ShowAsync);
    }

    private async Task HandleValidSubmit()
    {
        ClearModalMessage();

        if (currentException.EndDate < currentException.StartDate)
        {
            modalErrorMessage = "End date cannot be before the start date.";
            StateHasChanged();
            return;
        }

        if (!editContext.Validate())
        {
            return;
        }

        try
        {
            var httpClient = ClientFactory.CreateClient("Api");
            httpClient.DefaultRequestHeaders.Add("X-ORIG-NAME", userName);

            HttpResponseMessage response;

            bool isCreating = currentException.Id == 0;

            var commandModel = new
            {
                PracticeId = currentException.PracticeId,
                Description = currentException.Description,
                StartDate = currentException.StartDate,
                EndDate = currentException.EndDate,
                CreatedBy = isCreating ? userName : null,
                LastUpdatedBy = userName
            };

            if (isCreating)
            {
                response = await httpClient.PostAsJsonAsync("practiceopeninghoursexceptions", commandModel);
            }
            else
            {
                response = await httpClient.PutAsJsonAsync($"practiceopeninghoursexceptions/{currentException.Id}", commandModel);
            }

            if (response.IsSuccessStatusCode)
            {
                await addEditModal.HideAsync();
                await LoadExceptions();
                await ShowSuccessMessage("Exception saved successfully.");
            }
            else
            {
                modalErrorMessage = await response.Content.ReadAsStringAsync();
                if (string.IsNullOrWhiteSpace(modalErrorMessage))
                {
                    modalErrorMessage = "An unexpected error occurred while saving the exception.";
                }
                StateHasChanged();
            }
            

        }
        catch (Exception ex)
        {
            modalErrorMessage = "A network error occurred. Please try again.";
            Console.WriteLine($"Error saving exception: {ex.Message}");
            StateHasChanged();
        }
    }

    private async Task HandleConfirmDelete()
    {
        ClearModalMessage();
        try
        {
            var httpClient = ClientFactory.CreateClient("Api");
            httpClient.DefaultRequestHeaders.Add("X-ORIG-NAME", userName);

            var response = await httpClient.DeleteAsync($"practiceopeninghoursexceptions/{currentException.Id}");

            if (response.IsSuccessStatusCode)
            {
                await deleteConfirmModal.HideAsync();
                await LoadExceptions(); 
                await ShowSuccessMessage("Exception deleted successfully.");
            }
            else
            {
                modalErrorMessage = "The exception could not be deleted. Please try again.";
                StateHasChanged();
            }
        }
        catch (Exception ex)
        {
            modalErrorMessage = "A network error occurred. Please try again.";
            Console.WriteLine($"Error deleting exception: {ex.Message}");
            StateHasChanged();
        }
    }

    private async Task HandleCancel()
    {
        ClearModalMessage();
        await addEditModal.HideAsync();
        await deleteConfirmModal.HideAsync();
    }

    private async Task ShowSuccessMessage(string message)
    {
        ClearGeneralMessages();
        generalMessage = message;
        showGeneralSuccessMessage = true;
        StateHasChanged();

        await Task.Delay(3000);

        ClearGeneralMessages();
        StateHasChanged();
    }

    private void ClearGeneralMessages()
    {
        generalMessage = null;
        showGeneralSuccessMessage = false;
        showGeneralErrorMessage = false;
        showApiWarningMessage = false;
    }

    private void ClearModalMessage() => modalErrorMessage = null;
    
    private class ApiResponse<T>
    {
        public T? Records { get; set; }
    }
}