:root {
    /* Sidebar adjustments */
    --hx-sidebar-item-hover-background-color: var(--bs-secondary-color-rgb) !important;
    --hx-sidebar-item-active-background-color: var(--bs-secondary-color-rgb) !important;
    --hx-sidebar-item-active-color: var(--bs-secondary-color) !important;
    --hx-sidebar-item-hover-color: var(--bs-secondary-color) !important;
    --hx-sidebar-item-icon-color: var(--bs-secondary-color) !important;
    --hx-sidebar-parent-item-active-icon-color: var(--bs-secondary-color) !important;
    /* MyDentist Theming, overriding bootstrap primary */
    --myd-prime: #46aeab;
    --myd-dark-prime-5: hsl(from var(--myd-prime) h s calc(l - 5));
    --myd-dark-prime-10: hsl(from var(--myd-prime) h s calc(l - 10));
    --myd-light-prime-5: hsl(from var(--myd-prime) h s calc(l + 5));
    --bs-primary: var(--myd-prime) !important;
    --bs-link-color: : var(--myd-prime) !important;
    --bs-link-hover-color: : var(--myd-dark-prime-5) !important;
}

.hx-sidebar-brand-name {
    font-family: 'Roboto', sans-serif;
}

.btn-primary {
    --bs-btn-bg: var(--myd-prime);
    --bs-btn-border-color: var(--myd-prime);
    --bs-btn-hover-bg: var(--myd-dark-prime-5);
    --bs-btn-hover-border-color: var(--myd-dark-prime-5);
    --bs-btn-active-bg: var(--myd-dark-prime-10);
    --bs-btn-active-border-color: var(--myd-dark-prime-10);
    --bs-btn-disabled-bg: var(--myd-prime);
    --bs-btn-disabled-border-color: var(--myd-prime);
}

.pagination {
    --bs-pagination-active-bg: var(--myd-prime);
    --bs-pagination-active-border-color: var(--myd-prime);
}

h1:focus {
    outline: none;
}

.blazor-error-boundary {
    background: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTYiIGhlaWdodD0iNDkiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiIG92ZXJmbG93PSJoaWRkZW4iPjxkZWZzPjxjbGlwUGF0aCBpZD0iY2xpcDAiPjxyZWN0IHg9IjIzNSIgeT0iNTEiIHdpZHRoPSI1NiIgaGVpZ2h0PSI0OSIvPjwvY2xpcFBhdGg+PC9kZWZzPjxnIGNsaXAtcGF0aD0idXJsKCNjbGlwMCkiIHRyYW5zZm9ybT0idHJhbnNsYXRlKC0yMzUgLTUxKSI+PHBhdGggZD0iTTI2My41MDYgNTFDMjY0LjcxNyA1MSAyNjUuODEzIDUxLjQ4MzcgMjY2LjYwNiA1Mi4yNjU4TDI2Ny4wNTIgNTIuNzk4NyAyNjcuNTM5IDUzLjYyODMgMjkwLjE4NSA5Mi4xODMxIDI5MC41NDUgOTIuNzk1IDI5MC42NTYgOTIuOTk2QzI5MC44NzcgOTMuNTEzIDI5MSA5NC4wODE1IDI5MSA5NC42NzgyIDI5MSA5Ny4wNjUxIDI4OS4wMzggOTkgMjg2LjYxNyA5OUwyNDAuMzgzIDk5QzIzNy45NjMgOTkgMjM2IDk3LjA2NTEgMjM2IDk0LjY3ODIgMjM2IDk0LjM3OTkgMjM2LjAzMSA5NC4wODg2IDIzNi4wODkgOTMuODA3MkwyMzYuMzM4IDkzLjAxNjIgMjM2Ljg1OCA5Mi4xMzE0IDI1OS40NzMgNTMuNjI5NCAyNTkuOTYxIDUyLjc5ODUgMjYwLjQwNyA1Mi4yNjU4QzI2MS4yIDUxLjQ4MzcgMjYyLjI5NiA1MSAyNjMuNTA2IDUxWk0yNjMuNTg2IDY2LjAxODNDMjYwLjczNyA2Ni4wMTgzIDI1OS4zMTMgNjcuMTI0NSAyNTkuMzEzIDY5LjMzNyAyNTkuMzEzIDY5LjYxMDIgMjU5LjMzMiA2OS44NjA4IDI1OS4zNzEgNzAuMDg4N0wyNjEuNzk1IDg0LjAxNjEgMjY1LjM4IDg0LjAxNjEgMjY3LjgyMSA2OS43NDc1QzI2Ny44NiA2OS43MzA5IDI2Ny44NzkgNjkuNTg3NyAyNjcuODc5IDY5LjMxNzkgMjY3Ljg3OSA2Ny4xMTgyIDI2Ni40NDggNjYuMDE4MyAyNjMuNTg2IDY2LjAxODNaTTI2My41NzYgODYuMDU0N0MyNjEuMDQ5IDg2LjA1NDcgMjU5Ljc4NiA4Ny4zMDA1IDI1OS43ODYgODkuNzkyMSAyNTkuNzg2IDkyLjI4MzcgMjYxLjA0OSA5My41Mjk1IDI2My41NzYgOTMuNTI5NSAyNjYuMTE2IDkzLjUyOTUgMjY3LjM4NyA5Mi4yODM3IDI2Ny4zODcgODkuNzkyMSAyNjcuMzg3IDg3LjMwMDUgMjY2LjExNiA4Ni4wNTQ3IDI2My41NzYgODYuMDU0N1oiIGZpbGw9IiNGRkU1MDAiIGZpbGwtcnVsZT0iZXZlbm9kZCIvPjwvZz48L3N2Zz4=) no-repeat 1rem/1.8rem, #b32121;
    padding: 1rem 1rem 1rem 3.7rem;
    color: white;
}

    .blazor-error-boundary::after {
        content: "An error has occurred."
    }

input[type=checkbox] {
    outline: 2px solid #e9ecef;
}

.inner-wrapper-overlay {
    position: absolute;
    background: rgba(255,255,255,.1);
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    color: black;
    text-align: center;
    z-index: 10000;
}

.div-wrapper {
    position: relative;
    border-style: solid;
    border-width: 1px;
    border-color: rgba(255,255,255,.1);    
}

.team-card {
    border-radius: 1rem;
    border: 2px solid #6c757d; /* Darker gray for better visibility */
}

.avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 1.3rem;
    flex-shrink: 0;
}

h5.card-title {
    margin-bottom: 0.25rem;
}

/* General Styles */
.profile-title {
    font-size: 2rem;
    font-weight: 600;
    color: #007b7f;
    margin-bottom: 30px;
}

/* Label for section headers */
.label {
    font-weight: bold;
    font-size: 1.1rem;
    color: #4a4a4a;
    padding-left: 0;
    padding-right: 10px;
    text-align: right;
    line-height: 2.5;
}

/* Text content and input fields */
.row .col-md-10 {
    font-size: 1rem;
    color: #555;
    padding-left: 10px;
    line-height: 1.8;
    margin-top: 0.5rem;
}

/* Styling for input and textarea fields */
.input-field, .input-textarea {
    width: 100%;
    max-width: 500px;
    padding: 12px;
    border: 1px solid #ccc;
    border-radius: 8px;
    background-color: #f4f4f4;
    font-size: 1rem;
    color: #333;
    margin-top: -0.5rem;
    box-sizing: border-box;
    transition: border-color 0.3s ease-in-out;
}

    .input-field:focus, .input-textarea:focus {
        border-color: #007b7f;
        outline: none;
    }

.input-textarea {
    min-height: 250px;
    min-width: 300px;
    resize: vertical;
}

/* Section Spacing */
.mt-4 {
    margin-top: 30px;
}

.mb-4 {
    margin-bottom: 30px;
}

/* Button Styling */
.btn-primary {
    background-color: #007b7f;
    border-color: #007b7f;
    color: white;
    padding: 10px 20px;
    font-size: 1rem;
    border-radius: 6px;
}

    .btn-primary:hover {
        background-color: #006a6a;
        border-color: #006a6a;
    }

/* Alert Styling */
.alert-info {
    background-color: #e9f7f7;
    color: #5c6d7f;
    border-radius: 5px;
    font-size: 1rem;
    padding: 10px;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
    border-radius: 5px;
    font-size: 1rem;
    padding: 10px;
}

/* Button Alignment */
.btn-container {
    margin-top: 20px;
}

    .btn-container a {
        margin-right: 10px;
    }

/* Container for form and content */
.profile-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    background-color: #fff;
    box-shadow: 0px 2px 10px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
}
 