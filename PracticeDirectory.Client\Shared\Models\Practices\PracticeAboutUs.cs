﻿namespace PracticeDirectory.Client.Shared.Models.Practices
{
    public class PracticeAboutUs
    {
        public int id { get; set; }
        public int practiceId { get; set; }
        public string aboutUs { get; set; } = string.Empty;
        public string draftAboutUs { get; set; } = string.Empty;
        public int status { get; set; }
        public DateTime createdDate { get; set; }
        public DateTime lastUpdateDate { get; set; }
        public string createdBy { get; set; } = string.Empty;
        public string lastUpdateBy { get; set; } = string.Empty;
        public string rejectionNotes { get; set; } = string.Empty;
        public string practiceName { get; set; } = string.Empty;
    }
}
