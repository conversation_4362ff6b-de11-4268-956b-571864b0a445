﻿namespace PracticeDirectory.Client.Shared.Models.Practices
{
    public class PracticeAreaPerson
    {
        public int PracticeId { get; set; }
        public string AreaName { get; set; } = string.Empty;
        public string RoleName { get; set; } = string.Empty;
        public string PersonName { get; set; } = string.Empty;

        public string? EmailAddress { get; set; } = string.Empty;
        public string? PhoneNumber { get; set; } = string.Empty;
    }
}
