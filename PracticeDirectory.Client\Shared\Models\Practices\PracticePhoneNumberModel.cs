﻿namespace PracticeDirectory.Client.Shared.Models.Practices;

public class PracticePhoneNumberModel
{
	public int Id { get; set; }
	public int PracticeId { get; set; }
	public string? ContactMechanism { get; set; }
	public string? Purpose { get; set; }
	public string? Usage { get; set; }
	public string PhoneNumber { get; set; } = string.Empty;
	public bool UseForWebsite { get; set; }
	public string? CreatedBy { get; set; }
	public string? UpdatedBy { get; set; }
	public DateTime CreatedAt { get; set; }
	public DateTime UpdatedAt { get; set; }
}
