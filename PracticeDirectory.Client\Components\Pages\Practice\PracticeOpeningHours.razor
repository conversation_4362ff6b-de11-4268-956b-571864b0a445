﻿@* This is a reusable child component. It does not have its own @page directive. *@
@using Microsoft.AspNetCore.Components.Authorization
@using PracticeDirectory.Client.Shared.Configuration
@using PracticeDirectory.Client.Shared.Extensions
@using PracticeDirectory.Client.Shared.Models
@using PracticeDirectory.Client.Service
@using PracticeDirectory.Client.Shared.Models.Practices
@inject IHttpClientFactory ClientFactory
@inject UserContextService UserContext

<h3 class="mt-3 mb-2">Opening Hours</h3>
<style>
    /* Ensure all cards have the same height and width */
    .openinghoursprofile-card {
        min-width: 160px;
        max-width: 160px;
        height: 96px; /* Fixed height for uniform boxes */
        display: flex;
        flex-direction: column;
        justify-content: space-between; /* Space between content */
    }

    .openinghourscard {
        border: 1px solid #ddd;
        box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
    }
</style>

<div class="row">
    @for (int i = 1; i < 8; i++)
    {
        <div class="col openinghourscard openinghoursprofile-card p-3 px-4 shadow-sm rounded-3 text-center" style="margin-right: 1.5rem; margin-top: 1.5rem;">
            <div>
                <strong>@openingHoursModel.Days[i]</strong>
            </div>
            <div class="mt-2">
                @if (openingHoursModel.IsClosed[i])
                {
                    <strong>CLOSED</strong>
                }
                else
                {
                    <strong><span>@openingHoursModel.OpeningHours[i] - @openingHoursModel.ClosingHours[i]</span></strong>
                }
            </div>
        </div>
    }
</div>

<div class="row mt-3">
    @if (showInProgressWarningMessage)
    {
        <div class="alert alert-warning" role="alert">
            Requested Opening Hours changes are in progress. This can take up to 24 hours.
        </div>
    }
</div>

<div class="row mt-3">
    @if (showErrorMessage)
    {
        <div class="alert alert-danger" role="alert">
            Opening Hours could not be loaded at this time.
        </div>
    }
</div>

@* --- UPCOMING CLOSURES --- *@
@if (exceptions.Any())
{
    <div class="mt-3">
        <h3 class="mb-3">Upcoming Closures</h3>
        <div>
            @foreach (var exception in exceptions.OrderBy(e => e.StartDate))
            {
                <div class="border rounded-3 p-3 mb-3 d-flex align-items-center gap-4">
                    <div class="d-flex gap-2">
                        @if (exception.StartDate.HasValue && exception.EndDate.HasValue)
                        {
                            @foreach (var day in GetDates(exception.StartDate.Value, exception.EndDate.Value))
                            {
                                <div class="text-center bg-light p-2 rounded" style="width: 75px;">
                                    <div class="h5 mb-0 fw-bold">@day.ToString("dd")</div>
                                    <div class="text-uppercase small">@day.ToString("MMM")</div>
                                </div>
                            }
                        }
                    </div>
                    <div class="flex-grow-1">
                        <h5 class="mb-1">@exception.Description</h5>
                    </div>
                </div>
            }
        </div>
    </div>
}


<div class="d-flex flex-wrap gap-3 mt-4">
    @if (isAdmin || AuthExtensions.UserIsPracticeManagerForPracticeId(Practice.Id, currentUserPracticeId))
    {
        <a href="@($"openinghours/edit?practiceid={Practice.Id}")" class="btn btn-primary col-12 col-md-auto">Edit Opening Hours & Exceptions</a>
    }
</div>


@code {
    [Parameter]
    public DirectoryPracticeModel Practice { get; set; } = new();

    private DirectoryPracticeOpeningHoursModel openingHoursModel = new();
    private List<PracticeOpeningHoursExceptionModel> exceptions = new();
    private bool showErrorMessage = false;
    private bool showInProgressWarningMessage = false;
    private string isAuthenticated = string.Empty;

    [CascadingParameter]
    private Task<AuthenticationState> authenticationStateTask { get; set; } = default!;
    private string userName = string.Empty;
    private int currentUserPracticeId;
    private bool isAdmin = false;

    protected override async Task OnInitializedAsync()
    {
        var authState = await authenticationStateTask;
        userName = authState.GetWindowsUserName(UserContext.OverrideUsername);
        currentUserPracticeId = authState.GetPracticeId(ClientFactory, UserContext.OverrideUsername);
        isAdmin = AuthExtensions.IsUserAdmin(authState.GetOriginalWindowsUserName());

        // Load both opening hours and exceptions
        await GetOpeningHoursAsync();
        await GetOpeningHoursExceptionsAsync();
    }

    private async Task GetOpeningHoursExceptionsAsync()
    {
        try
        {
            var httpClient = ClientFactory.CreateClient("Api");
            httpClient.DefaultRequestHeaders.Add("X-ORIG-NAME", userName);
            var response = await httpClient.GetAsync($"practiceopeninghoursexceptions/practice/{Practice.Id}");
            response.EnsureSuccessStatusCode();

            var apiResult = await response.Content.ReadFromJsonAsync<ApiResponse<List<PracticeOpeningHoursExceptionModel>>>();
            exceptions = apiResult?.Records ?? new List<PracticeOpeningHoursExceptionModel>();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading exceptions on practice page: {ex.Message}.");
            exceptions = new List<PracticeOpeningHoursExceptionModel>();
        }
    }

    private class ApiResponse<T>
    {
        public T? Records { get; set; }
    }

    private IEnumerable<DateTime> GetDates(DateTime start, DateTime end)
    {
        for (DateTime date = start.Date; date <= end.Date; date = date.AddDays(1))
        {
            yield return date;
        }
    }

    private async Task GetOpeningHoursAsync()
    {
        // Name of client is critical for Windows Auth
        var httpClient = ClientFactory.CreateClient(ApiSettings.Api);
        httpClient.DefaultRequestHeaders.Add("X-ORIG-NAME", userName);

        try
        {
            showErrorMessage = false;
            showInProgressWarningMessage = false;

            var today = DateTime.Today;
            DateTime weekEndingDate = today.AddDays(-(int)today.DayOfWeek).AddDays(7);
            string formattedWeekEndingDate = weekEndingDate.ToString("yyyy-MM-dd");
            var response = await httpClient.GetFromJsonAsync<DirectoryPracticeOpeningHoursInfoFullModel>($"practiceopeninghours/{Practice.Id}?weekEndDate={formattedWeekEndingDate}");
            var practiceOpeningHoursInfoList = response?.Records.OrderBy(r => r.Day_Of_Week).ToList() ?? [];

            if (practiceOpeningHoursInfoList.Count() > 0)
            {
                if (practiceOpeningHoursInfoList[0].In_Progress)
                {
                    showInProgressWarningMessage = true;
                }
            }

            openingHoursModel = ConvertToOpeningHoursView(practiceOpeningHoursInfoList);

        }
        catch (Exception)
        {
            showErrorMessage = true;
        }
    }

    private DirectoryPracticeOpeningHoursModel ConvertToOpeningHoursView(List<DirectoryPracticeOpeningHoursInfoModel> practiceOpeningHoursInfoList)
    {
        DirectoryPracticeOpeningHoursModel res = new();
        for (int i = 1; i < 8; i++)
        {
            var item = practiceOpeningHoursInfoList.Where(_ => _.Day_Of_Week == i).FirstOrDefault();

            if (item == null)
            {
                res.IsClosed[i] = true;
                res.OpeningHours[i] = "CLOSED";
                res.ClosingHours[i] = "CLOSED";
            }
            else
            {
                if (item.Is_Closed == 1)
                {
                    res.IsClosed[i] = true;
                    res.OpeningHours[i] = "CLOSED";
                    res.ClosingHours[i] = "CLOSED";
                }
                else
                {
                    res.IsClosed[i] = false;
                    if (!string.IsNullOrEmpty(item.Opening_Time_Name))
                    {
                        res.OpeningHours[i] = item.Opening_Time_Name;
                    }

                    if (!string.IsNullOrEmpty(item.Closing_Time_Name))
                    {
                        res.ClosingHours[i] = item.Closing_Time_Name;
                    }
                }
            }
        }

        res.ImportStatus = "Processing";
        res.PracticeId = Practice.Id;

        return res;
    }
}