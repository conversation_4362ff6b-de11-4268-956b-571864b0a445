namespace PracticeDirectory.Client.Service
{
    public class ImageCacheItem
    {
        public string ImagePath { get; set; } = string.Empty;
        public string DataUrl { get; set; } = string.Empty;
        public DateTime CacheTime { get; set; } = DateTime.Now;
    }

    public interface IFileStorageHubService
    {
        Task<string> UploadImageAsync(string base64Data, string personalNumber, string fullName);
        Task<byte[]> DownloadImageAsync(string imagePath);
        Task<Dictionary<string, string>> DownloadImagesAsync(List<string> imagePaths);
        Task<string> GetImageDataUrlAsync(string imagePath);
        void CacheImageDataUrl(string imagePath, string dataUrl);
        Dictionary<string, ImageCacheItem> GetImageCache();
    }
}
