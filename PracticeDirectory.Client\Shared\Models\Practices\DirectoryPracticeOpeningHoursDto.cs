﻿namespace PracticeDirectory.Client.Shared.Models.Practices;

public class DirectoryPracticeOpeningHoursDto
{
	public int PracticeId { get; set; }	
	public string[] OpeningHours { get; set; } = new string[8];
	public string[] ClosingHours { get; set; } = new string[8];
	public bool[] IsClosed { get; set; } = new bool[8];
	public string LastUpdatedBy { get; set; } = string.Empty;
	public DateTime LastUpdateDate { get; set; } = DateTime.Now;
}
