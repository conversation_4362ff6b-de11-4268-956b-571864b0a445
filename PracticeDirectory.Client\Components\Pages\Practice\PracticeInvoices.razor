@* This is a reusable child component. It does not have its own @page directive. *@
@using Microsoft.AspNetCore.Components.Authorization
@using PracticeDirectory.Client.Shared.Configuration
@using PracticeDirectory.Client.Shared.Extensions
@using PracticeDirectory.Client.Shared.Models
@using PracticeDirectory.Client.Service
@using PracticeDirectory.Client.Shared.Models.Practices
@inject IHttpClientFactory ClientFactory
@inject UserContextService UserContext


<div class="d-flex flex-wrap gap-3 mt-4">
    @if (isAdmin || AuthExtensions.UserIsPracticeManagerForPracticeId(Practice.Id, currentUserPracticeId))
    {
        <a href="http://myd-web-new/practiceview/practice/details/@PracticeShortId" class="btn btn-primary col-12 col-md-auto">View Lab Invoices</a>
    }
</div>


@code {
    [Parameter]
    public DirectoryPracticeModel Practice { get; set; } = new();
    [CascadingParameter]
    private Task<AuthenticationState> authenticationStateTask { get; set; } = default!;
    private string userName = string.Empty;
    private int currentUserPracticeId;
    private bool isAdmin = false;

    private string PracticeShortId => (Practice.Id % 1000).ToString();

    protected override async Task OnInitializedAsync()
    {
        var authState = await authenticationStateTask;
        userName = authState.GetWindowsUserName(UserContext.OverrideUsername);
        currentUserPracticeId = authState.GetPracticeId(ClientFactory, UserContext.OverrideUsername);
        isAdmin = AuthExtensions.IsUserAdmin(authState.GetOriginalWindowsUserName());        
    }

}