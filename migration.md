# Lab Invoice Functionality Migration Plan (Final)

This document provides a complete, step-by-step guide for migrating the lab invoice functionality to the PracticeDirectory.Client Blazor application, consolidating it into a single, self-contained component as requested.

## 1. Backend Setup

### 1.1. NuGet Packages

1.  **Install necessary packages:** Add the following NuGet packages to the `PracticeDirectory.Client` project.

    ```bash
    dotnet add package Dapper
    dotnet add package Microsoft.Data.SqlClient
    dotnet add package ClosedXML
    ```

### 1.2. Configuration

1.  **Add Connection Strings:** In `appsettings.json`, add the connection strings for the `WarehouseNavision` and `Navision` databases.

    ```json
    {
      "ConnectionStrings": {
        "WarehouseNavision": "Data Source=YOUR_SERVER;Initial Catalog=WarehouseNavision;Integrated Security=True;TrustServerCertificate=True",
        "Navision": "Data Source=YOUR_SERVER;Initial Catalog=Navision;Integrated Security=True;TrustServerCertificate=True"
      }
    }
    ```

### 1.3. Enums & Models

1.  **Create Enums and Models:** Create the necessary enum and model files.

    `PracticeDirectory.Client/Shared/Enums/InvoiceOnHoldStatus.cs`
    ```csharp
    namespace PracticeDirectory.Client.Shared.Enums
    {
        public enum InvoiceOnHoldStatus
        {
            NotOnHold,
            Queried
        }
    }
    ```

    `PracticeDirectory.Client/Shared/Models/LabInvoices/LabInvoiceModel.cs`
    ```csharp
    using System;
    namespace PracticeDirectory.Client.Shared.Models.LabInvoices
    {
        public class LabInvoiceModel
        {
            public decimal Amount { get; set; }
            public DateTime DocumentDateTime { get; set; }
            public string InvoiceNumber { get; set; }
            public bool IsInQuery { get; set; }
            public string LabCode { get; set; }
            public string LabName { get; set; }
            public int LedgerEntryId { get; set; }
            public string LineType { get; set; }
            public string OnHold { get; set; }
            public string Performer { get; set; }
            public string PINumber { get; set; }
            public DateTime PostingDate { get; set; }
            public int PracticeNumber { get; set; }
            public bool Selected { get; set; }
            public string WorkType { get; set; }
        }
    }
    ```

    `PracticeDirectory.Client/Shared/Models/LabInvoices/LabInvoiceAuditModel.cs`
    ```csharp
    using System;

    namespace PracticeDirectory.Client.Shared.Models.LabInvoices
    {
        public class LabInvoiceAuditModel
        {
            public DateTime DateTimeUpdated { get; set; }
            public string Field { get; set; }
            public int LabAuthorisationId { get; set; }
            public string LabCode { get; set; }
            public string LabName { get; set; }
            public int? LedgerEntryId { get; set; }
            public string NewValue { get; set; }
            public string OldValue { get; set; }
            public string RecordID { get; set; }
            public string UserUpdated { get; set; }
        }
    }
    ```

    `PracticeDirectory.Client/Shared/Models/LabInvoices/LabInvoiceSummaryModel.cs`
    ```csharp
    namespace PracticeDirectory.Client.Shared.Models.LabInvoices
    {
        public class LabInvoiceSummaryModel
        {
            public int PracticeId { get; set; }
            public string PracticeName { get; set; }
            public int Count { get; set; }
            public int InQuery { get; set; }
        }
    }
    ```

    `PracticeDirectory.Client/Shared/Models/LabInvoices/GetUnapprovedCountRequest.cs`
    ```csharp
    using System.Collections.Generic;

    namespace PracticeDirectory.Client.Shared.Models.LabInvoices
    {
        public class GetUnapprovedCountRequest
        {
            public List<string> Practices { get; set; }
            public string PracticeQuery { get; set; }
        }
    }
    ```

### 1.4. Data Access Service

1.  **Create Lab Invoice Service Interface:** The interface must include a parameter to pass the current user's identity for auditing purposes.

    `PracticeDirectory.Client/Service/ILabInvoiceService.cs`
    ```csharp
    using System.Collections.Generic;
    using System.Threading.Tasks;
    using PracticeDirectory.Client.Shared.Models.LabInvoices;

    namespace PracticeDirectory.Client.Service
    {
        public interface ILabInvoiceService
        {
            Task<List<LabInvoiceModel>> GetAllUnapprovedInvoices(int practiceNumber);
            Task<LabInvoiceModel> GetByLedgerEntryId(int ledgerEntryId);
            Task<LabInvoiceModel> GetLabInvoiceForPermissionCheck(List<int> ledgerEntryIds);
            Task PlaceOnHold(List<int> ledgerEntryIds, string userName);
            Task RemoveOnHoldFlag(List<int> ledgerEntryIds, string userName);
            Task<byte[]> GenerateInvoicesExcel(List<LabInvoiceModel> invoices);
            Task<List<LabInvoiceAuditModel>> SearchForLabAuthorisations(string recordID, DateTime? startDateTime, DateTime? endDateTime, string userUpdated);
            Task<List<LabInvoiceSummaryModel>> GetUnapprovedInvoicesCount(Dictionary<int, string> allPractices, List<int> filterPractices = null);
        }
    }
    ```

2.  **Implement Lab Invoice Service:** The implementation must include:
    *   **Credit Note Logic:** Transform the `Amount` for credit notes.
    *   **Audit Trail Logic:** Insert a record into `LabAuthorisationAudit` whenever an invoice status is changed.
    *   **DataRow Extension Logic:** Replicate the `GetColumnValueOrDefault` logic from `DataRowExtension.cs` when mapping data from Dapper queries to `LabInvoiceModel` properties, especially for handling `DBNull.Value` and default values.

    `PracticeDirectory.Client/Service/LabInvoiceService.cs`
    ```csharp
    using System.Collections.Generic;
    using System.Data;
    using System.IO;
    using System.Linq;
    using System.Threading.Tasks;
    using ClosedXML.Excel;
    using Dapper;
    using Microsoft.Data.SqlClient;
    using Microsoft.Extensions.Configuration;
    using PracticeDirectory.Client.Shared.Enums;
    using PracticeDirectory.Client.Shared.Models.LabInvoices;
    using PracticeDirectory.Client.Shared.Helpers;
    using System;
    using System.Text;

    namespace PracticeDirectory.Client.Service
    {
        public class LabInvoiceService : ILabInvoiceService
        {
            private readonly string _warehouseNavisionConnectionString;
            private readonly string _navisionConnectionString;

            public LabInvoiceService(IConfiguration configuration)
            {
                _warehouseNavisionConnectionString = configuration.GetConnectionString("WarehouseNavision");
                _navisionConnectionString = configuration.GetConnectionString("Navision");
            }

            public async Task<List<LabInvoiceModel>> GetAllUnapprovedInvoices(int practiceNumber)
            {
                const string sql =
                    "SELECT * FROM View_NavisionUnauthorisedInvoices WHERE [Practice ID] = @practiceNumber ORDER BY [Document Date]";

                using (var connection = new SqlConnection(_warehouseNavisionConnectionString))
                {
                    var invoices = (await connection.QueryAsync<dynamic>(sql, new { practiceNumber })).AsList();
                    var mappedInvoices = new List<LabInvoiceModel>();

                    foreach (var item in invoices)
                    {
                        var invoice = new LabInvoiceModel
                        {
                            InvoiceNumber = item."Invoice Number",
                            PINumber = item.PINumber,
                            LabCode = item."Lab Code",
                            LabName = item."Lab Name",
                            WorkType = item."Work Type",
                            PracticeNumber = item."Practice ID",
                            Performer = item.Performer,
                            PostingDate = item."Posting Date",
                            OnHold = item."On Hold",
                            LedgerEntryId = item."Ledger Entry Id",
                            DocumentDateTime = item."Document Date",
                            LineType = item.LineType,
                            Amount = item.Amount
                        };

                        // Replicate credit note logic
                        if (invoice.LineType.Equals("credit note", StringComparison.OrdinalIgnoreCase))
                        {
                            invoice.Amount *= -1;
                        }

                        // Replicate IsInQuery logic
                        invoice.IsInQuery = invoice.OnHold.Equals("Y QUERY", StringComparison.OrdinalIgnoreCase);

                        mappedInvoices.Add(invoice);
                    }
                    return mappedInvoices;
                }
            }

            public async Task<LabInvoiceModel> GetByLedgerEntryId(int ledgerEntryId)
            {
                const string sql = "SELECT * FROM View_NavisionUnauthorisedInvoices WHERE [Ledger Entry Id] = @ledgerEntryId";

                using (var connection = new SqlConnection(_warehouseNavisionConnectionString))
                {
                    var invoiceData = (await connection.QueryFirstOrDefaultAsync<dynamic>(sql, new { ledgerEntryId }));
                    if (invoiceData == null)
                    {
                        throw new LabInvoiceMissingException(ledgerEntryId);
                    }

                    var invoice = new LabInvoiceModel
                    {
                        InvoiceNumber = invoiceData."Invoice Number",
                        PINumber = invoiceData.PINumber,
                        LabCode = invoiceData."Lab Code",
                        LabName = invoiceData."Lab Name",
                        WorkType = invoiceData."Work Type",
                        PracticeNumber = invoiceData."Practice ID",
                        Performer = invoiceData.Performer,
                        PostingDate = invoiceData."Posting Date",
                        OnHold = invoiceData."On Hold",
                        LedgerEntryId = invoiceData."Ledger Entry Id",
                        DocumentDateTime = invoiceData."Document Date",
                        LineType = invoiceData.LineType,
                        Amount = invoiceData.Amount
                    };

                    if (invoice.LineType.Equals("credit note", StringComparison.OrdinalIgnoreCase))
                    {
                        invoice.Amount *= -1;
                    }
                    invoice.IsInQuery = invoice.OnHold.Equals("Y QUERY", StringComparison.OrdinalIgnoreCase);

                    return invoice;
                }
            }

            public async Task<LabInvoiceModel> GetLabInvoiceForPermissionCheck(List<int> ledgerEntryIds)
            {
                LabInvoiceModel matchingLabInvoice = null;
                int ledgerEntryIndex = 0;

                while (ledgerEntryIndex < ledgerEntryIds.Count && matchingLabInvoice == null)
                {
                    int ledgerEntryId = ledgerEntryIds[ledgerEntryIndex];

                    try
                    {
                        matchingLabInvoice = await GetByLedgerEntryId(ledgerEntryId);
                    }
                    catch (LabInvoiceMissingException)
                    {
                        // Silent Exception - invoice will already have been approved.
                    }

                    ledgerEntryIndex++;
                }

                if (matchingLabInvoice == null)
                {
                    throw new LabInvoiceMissingException("No Invoices are there to calculate the Practice Id.");
                }

                return matchingLabInvoice;
            }

            public async Task PlaceOnHold(List<int> ledgerEntryIds, string userName)
            {
                const string updateSql =
                    "UPDATE [Petrie Tucker & Partners Ltd$Vendor Ledger Entry] SET [On Hold] = 'Y QUERY' WHERE [Entry No_] = @EntryNo";
                const string auditSql =
                    "INSERT INTO dbo.LabAuthorisationAudit (RecordID, OldValue, NewValue, Field, DateTimeUpdated, UserUpdated, LedgerEntryId) VALUES (@RecordID, @OldValue, @NewValue, @Field, @DateTimeUpdated, @UserUpdated, @LedgerEntryId)";

                using (var navConnection = new SqlConnection(_navisionConnectionString))
                using (var warehouseConnection = new SqlConnection(_warehouseNavisionConnectionString))
                {
                    foreach (var id in ledgerEntryIds)
                    {
                        var currentInvoice = await GetByLedgerEntryId(id);
                        if (currentInvoice == null) continue;

                        await navConnection.ExecuteAsync(updateSql, new { EntryNo = id });

                        await warehouseConnection.ExecuteAsync(auditSql, new
                        {
                            RecordID = currentInvoice.InvoiceNumber,
                            OldValue = currentInvoice.OnHold,
                            NewValue = "Y QUERY",
                            Field = "On Hold",
                            DateTimeUpdated = DateTime.Now,
                            UserUpdated = userName,
                            LedgerEntryId = id
                        });
                    }
                }
            }

            public async Task RemoveOnHoldFlag(List<int> ledgerEntryIds, string userName)
            {
                const string updateSql =
                    "UPDATE [Petrie Tucker & Partners Ltd$Vendor Ledger Entry] SET [On Hold] = '' WHERE [Entry No_] = @EntryNo";
                const string auditSql =
                    "INSERT INTO dbo.LabAuthorisationAudit (RecordID, OldValue, NewValue, Field, DateTimeUpdated, UserUpdated, LedgerEntryId) VALUES (@RecordID, @OldValue, @NewValue, @Field, @DateTimeUpdated, @UserUpdated, @LedgerEntryId)";

                using (var navConnection = new SqlConnection(_navisionConnectionString))
                using (var warehouseConnection = new SqlConnection(_warehouseNavisionConnectionString))
                {
                    foreach (var id in ledgerEntryIds)
                    {
                        var currentInvoice = await GetByLedgerEntryId(id);
                        if (currentInvoice == null) continue;

                        await navConnection.ExecuteAsync(updateSql, new { EntryNo = id });

                        await warehouseConnection.ExecuteAsync(auditSql, new
                        {
                            RecordID = currentInvoice.InvoiceNumber,
                            OldValue = currentInvoice.OnHold,
                            NewValue = "",
                            Field = "On Hold",
                            DateTimeUpdated = DateTime.Now,
                            UserUpdated = userName,
                            LedgerEntryId = id
                        });
                    }
                }
            }

            public async Task<byte[]> GenerateInvoicesExcel(List<LabInvoiceModel> invoices)
            {
                using (var workbook = new XLWorkbook())
                {
                    var worksheet = workbook.Worksheets.Add("Invoices");
                    worksheet.Cell(1, 1).InsertTable(invoices);
                    worksheet.Columns().AdjustToContents();

                    using (var stream = new MemoryStream())
                    {
                        workbook.SaveAs(stream);
                        return stream.ToArray();
                    }
                }
            }

            public async Task<List<LabInvoiceAuditModel>> SearchForLabAuthorisations(string recordID, DateTime? startDateTime, DateTime? endDateTime, string userUpdated)
            {
                StringBuilder sqlBuilder = new StringBuilder(@"SELECT [LabAuthorisationAuditID]
                      ,[RecordID]
                      ,[LedgerEntryId]
                      ,[OldValue]
                      ,[NewValue]
                      ,[Field]
                      ,[DateTimeUpdated]
                      ,[UserUpdated]
                      , head.[Vendor Invoice No_] as [Invoice Number]
                      , head.[Pay-to Vendor No_] as [LabCode]
                      , head.[Pay-to Name] as [LabName]
                  FROM [Warehouse].[dbo].[LabAuthorisationAudit] LA
                  LEFT JOIN SQL5.Nav.[dbo].[Petrie Tucker & Partners Ltd$Vendor Ledger Entry] vend WITH(NOLOCK)  ON vend.[Entry No_] = LA.LedgerEntryId
                  LEFT JOIN SQL5.Nav.[dbo].[Petrie Tucker & Partners Ltd$Purch_ Inv_ Header] head WITH(NOLOCK) ON head.No_ = vend.[Document No_] WHERE 1=1 ");

                var parameters = new DynamicParameters();

                if (!string.IsNullOrEmpty(recordID))
                {
                    sqlBuilder.Append("AND RecordID LIKE '%' + @RecordID + '%' ");
                    parameters.Add("RecordID", recordID);
                }

                if (!string.IsNullOrEmpty(userUpdated))
                {
                    sqlBuilder.Append("AND UserUpdated LIKE '%' + @UserUpdated + '%' ");
                    parameters.Add("UserUpdated", userUpdated);
                }

                if (null != startDateTime)
                {
                    sqlBuilder.Append("AND DateTimeUpdated > @StartDateTime ");
                    parameters.Add("StartDateTime", startDateTime);
                }

                if (null != endDateTime)
                {
                    sqlBuilder.Append("AND DateTimeUpdated < @EndDateTime ");
                    parameters.Add("EndDateTime", endDateTime);
                }

                using (var connection = new SqlConnection(_warehouseNavisionConnectionString))
                {
                    var audits = (await connection.QueryAsync<dynamic>(sqlBuilder.ToString(), parameters)).AsList();
                    var mappedAudits = new List<LabInvoiceAuditModel>();

                    foreach (var item in audits)
                    {
                        mappedAudits.Add(new LabInvoiceAuditModel
                        {
                            LabAuthorisationId = item.LabAuthorisationAuditID,
                            RecordID = item.RecordID,
                            OldValue = item.OldValue,
                            NewValue = item.NewValue,
                            Field = item.Field,
                            UserUpdated = item.UserUpdated,
                            LedgerEntryId = item.LedgerEntryId,
                            LabCode = item.LabCode,
                            LabName = item.LabName,
                            DateTimeUpdated = item.DateTimeUpdated
                        });
                    }
                    return mappedAudits;
                }
            }

            public async Task<List<LabInvoiceSummaryModel>> GetUnapprovedInvoicesCount(Dictionary<int, string> allPractices, List<int> filterPractices = null)
            {
                List<LabInvoiceSummaryModel> labInvoiceSummaryModel = new List<LabInvoiceSummaryModel>();
                StringBuilder sqlBuilder = new StringBuilder();

                if (filterPractices != null && filterPractices.Any())
                {
                    string inSql = string.Join(",", filterPractices);
                    sqlBuilder.AppendFormat("SELECT COUNT([Invoice Number]) AS Count, SUM(CASE LEN([On Hold]) WHEN 1 THEN 0 ELSE 1 END) AS Inquery,[Practice Id] AS PracticeId FROM View_NavisionUnauthorisedInvoices WHERE [Practice Id] IN ({0}) GROUP BY [Practice Id]", inSql);
                }
                else
                {
                    sqlBuilder.Append("SELECT COUNT([Invoice Number]) AS Count, SUM(CASE LEN([On Hold]) WHEN 1 THEN 0 ELSE 1 END) AS Inquery,[Practice Id] AS PracticeId FROM View_NavisionUnauthorisedInvoices GROUP BY [Practice Id]");
                }

                using (var connection = new SqlConnection(_warehouseNavisionConnectionString))
                {
                    var data = (await connection.QueryAsync<dynamic>(sqlBuilder.ToString())).AsList();

                    foreach (var item in data)
                    {
                        int practiceId = item.PracticeId;
                        string practiceName = allPractices.ContainsKey(practiceId) ? allPractices[practiceId] : null;

                        if (!string.IsNullOrEmpty(practiceName))
                        {
                            labInvoiceSummaryModel.Add(new LabInvoiceSummaryModel
                            {
                                PracticeId = practiceId,
                                PracticeName = practiceName,
                                Count = item.Count,
                                InQuery = item.Inquery
                            });
                        }
                    }

                    // Add practices with 0 count if they were in the filter but not in the results
                    if (filterPractices != null)
                    {
                        foreach (int practiceId in filterPractices)
                        {
                            if (!labInvoiceSummaryModel.Any(x => x.PracticeId == practiceId))
                            {
                                labInvoiceSummaryModel.Add(new LabInvoiceSummaryModel
                                {
                                    PracticeId = practiceId,
                                    PracticeName = allPractices.ContainsKey(practiceId) ? allPractices[practiceId] : null,
                                    Count = 0,
                                    InQuery = 0
                                });
                            }
                        }
                    }
                }

                return labInvoiceSummaryModel.OrderBy(l => l.PracticeName).ToList();
            }
        }
    }
    ```

3.  **Register the Service:** In `Program.cs`, register the `LabInvoiceService` for dependency injection.

## 2. Frontend

### 2.1. JavaScript Interop for File Downloads

1.  **Create and include `wwwroot/js/fileDownloader.js`**.

### 2.2. Lab Invoices Grid (PracticeInvoices.razor)

1.  **Update the component:** The component must include:
    *   **Authorization:** Controls are disabled if the user is not authorized.
    *   **Checkbox Logic:** Methods to handle the interdependent "Approve" and "In Query" states.
    *   **"Check All" Logic:** A method that correctly toggles the "Approve" checkbox only for items that are not already in query.
    *   **User Feedback:** Use `IHxMessenger` for notifications.
    *   **Custom Row Styling:** A method to provide a CSS class to the grid rows to visually group items by `LedgerEntryId`.

    `PracticeDirectory.Client/Components/Pages/Practice/PracticeInvoices.razor`
    ```razor
    @page "/practice/details/{PracticeId:int}"
    @using PracticeDirectory.Client.Shared.Models.LabInvoices
    @using PracticeDirectory.Client.Service
    @using PracticeDirectory.Client.Shared.Extensions
    @using PracticeDirectory.Client.Shared.Configuration
    @inject ILabInvoiceService LabInvoiceService
    @inject IJSRuntime JSRuntime
    @inject IUserContextService UserContextService
    @inject IOptions<ApiSettings> ApiSettings
    @inject IHxMessenger Messenger

    <h3>Lab Invoices</h3>

    <div class="rowContainer">
        <div class="rowContainer_field">
            <input class="rowContainer_button-alt" type="button" @onclick="SubmitInvoices" value="Submit" disabled="@(!_canPerformActions)" />
            <input class="rowContainer_button-alt" type="button" @onclick="CheckAll" value="@(_allChecked ? "Uncheck All" : "Check All")" id="input-checkall" disabled="@(!_canPerformActions)" />
        </div>
    </div>

    @if (isLoading)
    {
        <div class="loader">
            <h1>Loading Invoices...</h1>
            <div>
                <img src="/Images/loading.gif" />
            </div>
        </div>
    }
    else
    {
        <HxGrid TItem="LabInvoiceModel" DataProvider="@GetInvoices" RowClass="@GetRowClass">
            <Columns>
                <HxGridColumn HeaderText="Approve" Sortable="false">
                    <Template>
                        <input type="checkbox" @onchange="(e) => OnApprovalChanged(e, Context)" @bind="Context.Selected" disabled="@(!_canPerformActions || Context.IsInQuery)" />
                    </Template>
                </HxGridColumn>
                <HxGridColumn HeaderText="In Query" Sortable="false">
                    <Template>
                        <input type="checkbox" @onchange="(e) => OnInQueryChanged(e, Context)" @bind="Context.IsInQuery" disabled="@(!_canPerformActions || Context.Selected)" />
                    </Template>
                </HxGridColumn>
                <HxGridColumn @bind-Value="Context.LedgerEntryId" HeaderText="LedgerEntryId" Visible="false" />
                <HxGridColumn @bind-Value="Context.LabName" HeaderText="Lab Name" />
                <HxGridColumn @bind-Value="Context.LabCode" HeaderText="Lab Code" />
                <HxGridColumn @bind-Value="Context.Performer" HeaderText="Performer" />
                <HxGridColumn @bind-Value="Context.InvoiceNumber" HeaderText="Invoice Number" />
                <HxGridColumn @bind-Value="Context.PINumber" HeaderText="PI Number" />
                <HxGridColumn @bind-Value="Context.WorkType" HeaderText="Work Type" />
                <HxGridColumn @bind-Value="Context.Amount" HeaderText="Amount" Format="C" />
                <HxGridColumn @bind-Value="Context.DocumentDateTime" HeaderText="Document Date" Format="d" />
                <HxGridColumn @bind-Value="Context.OnHold" HeaderText="On Hold" />
                <HxGridColumn @bind-Value="Context.LineType" HeaderText="Line Type" />
            </Columns>
        </HxGrid>
    }

    @code {
        [Parameter]
        public int PracticeId { get; set; }

        private List<LabInvoiceModel> invoices = new List<LabInvoiceModel>();
        private bool isLoading = true;
        private bool _allChecked = false;
        private int _lastLedgerEntryId = 0;
        private bool _isOddRow = true;
        private bool _canPerformActions = false;

        protected override async Task OnInitializedAsync()
        {
            await LoadInvoices();
            await CheckUserPermissions();
        }

        private async Task LoadInvoices()
        {
            isLoading = true;
            invoices = await LabInvoiceService.GetAllUnapprovedInvoices(PracticeId);
            isLoading = false;
        }

        private async Task CheckUserPermissions()
        {
            var currentUser = await UserContextService.GetCurrentUser();
            if (currentUser != null)
            {
                // Replicate LabInvoiceModel.HasPermission logic
                _canPerformActions = currentUser.IsAdm() ||
                                     currentUser.IsClaritySuperUser() ||
                                     (currentUser.IsPm() && PracticeId.ToString() == currentUser.GetLocationForLabInvoices());
            }
        }

        private string GetRowClass(LabInvoiceModel item)
        {
            if (item.LedgerEntryId != _lastLedgerEntryId)
            {
                _isOddRow = !_isOddRow;
                _lastLedgerEntryId = item.LedgerEntryId;
            }
            return _isOddRow ? "grid_rowoveride-odd" : "grid_rowoveride-even";
        }

        private void CheckAll()
        {
            _allChecked = !_allChecked;
            foreach (var invoice in invoices)
            {
                if (!invoice.IsInQuery) // Only check/uncheck if not already in query
                {
                    invoice.Selected = _allChecked;
                }
            }
            StateHasChanged();
        }

        private void OnApprovalChanged(ChangeEventArgs e, LabInvoiceModel item)
        {
            bool isChecked = (bool)e.Value;
            item.Selected = isChecked;
            if (isChecked)
            {
                item.IsInQuery = false;
            }

            // Replicate checkApproveFriends: affect all invoices with the same LedgerEntryId
            foreach (var inv in invoices.Where(i => i.LedgerEntryId == item.LedgerEntryId))
            {
                inv.Selected = isChecked;
                if (isChecked)
                {
                    inv.IsInQuery = false;
                }
            }
            StateHasChanged();
        }

        private void OnInQueryChanged(ChangeEventArgs e, LabInvoiceModel item)
        {
            bool isChecked = (bool)e.Value;
            item.IsInQuery = isChecked;
            if (isChecked)
            {
                item.Selected = false;
            }

            // Replicate checkQueryFriends: affect all invoices with the same LedgerEntryId
            foreach (var inv in invoices.Where(i => i.LedgerEntryId == item.LedgerEntryId))
            {
                inv.IsInQuery = isChecked;
                if (isChecked)
                {
                    inv.Selected = false;
                }
            }
            StateHasChanged();
        }

        private async Task SubmitInvoices()
        {
            var invoicesToApprove = invoices.Where(i => i.Selected).Select(i => i.LedgerEntryId).ToList();
            var invoicesToPutOnHold = invoices.Where(i => i.IsInQuery).Select(i => i.LedgerEntryId).ToList();

            var currentUser = await UserContextService.GetCurrentUser();
            string userName = currentUser?.GetAdUsername() ?? "Unknown";

            if (invoicesToPutOnHold.Any())
            {
                try
                {
                    await LabInvoiceService.PlaceOnHold(invoicesToPutOnHold, userName);
                    Messenger.Notify(new MessengerEntry(MessengerEntryType.Success, $"Successfully put {invoicesToPutOnHold.Count} Invoices In Query!"));
                }
                catch (LabInvoiceMissingException ex)
                {
                    Messenger.Notify(new MessengerEntry(MessengerEntryType.Error, ex.Message));
                }
                catch (Exception ex)
                {
                    Messenger.Notify(new MessengerEntry(MessengerEntryType.Error, $"Error placing invoices on hold: {ex.Message}"));
                }
            }

            if (invoicesToApprove.Any())
            {
                try
                {
                    await LabInvoiceService.RemoveOnHoldFlag(invoicesToApprove, userName);
                    Messenger.Notify(new MessengerEntry(MessengerEntryType.Success, $"Successfully Approved {invoicesToApprove.Count} Invoices!"));
                }
                catch (LabInvoiceMissingException ex)
                {
                    Messenger.Notify(new MessengerEntry(MessengerEntryType.Error, ex.Message));
                }
                catch (Exception ex)
                {
                    Messenger.Notify(new MessengerEntry(MessengerEntryType.Error, $"Error approving invoices: {ex.Message}"));
                }
            }

            if (!invoicesToApprove.Any() && !invoicesToPutOnHold.Any())
            {
                Messenger.Notify(new MessengerEntry(MessengerEntryType.Warning, "Nothing to approve or put on hold, please select at least 1 Lab Invoice before clicking 'Submit'."));
            }

            await LoadInvoices(); // Reload to reflect changes
            StateHasChanged();
        }
    }
    ```

### 2.3. Lab Invoice Audit Report (LabInvoiceAuditReport.razor)

1.  **Create a new Blazor component** for the Lab Invoice Audit Report. This component will replace `ViewReport.cshtml` and the audit-related logic from `LabInvoicesBindings.js`.

    `PracticeDirectory.Client/Components/Pages/LabInvoices/LabInvoiceAuditReport.razor`
    ```razor
    @page "/labinvoiceauditreport"
    @using PracticeDirectory.Client.Shared.Models.LabInvoices
    @using PracticeDirectory.Client.Service
    @inject ILabInvoiceService LabInvoiceService
    @inject IJSRuntime JSRuntime

    <h3>Lab Invoice Reporting</h3>

    <div class="formContainer_title-smaller formContainer_title-view">
        <table class="table-searchfilter">
            <tr>
                <td>Record Id</td>
                <td><input type="text" @bind="RecordId" /></td>
            </tr>
            <tr>
                <td>Start Date Time</td>
                <td><input type="datetime-local" @bind="StartDateTime" /></td>
            </tr>
            <tr>
                <td>End Date Time</td>
                <td><input type="datetime-local" @bind="EndDateTime" /></td>
            </tr>
            <tr>
                <td></td>
                <td></td>
            </tr>
            <tfoot>
                <tr>
                    <td colspan="2">
                        <button class="btn btn-primary" @onclick="SearchAudits">Search</button>
                    </td>
                </tr>
            </tfoot>
        </table>
    </div>

    @if (isLoading)
    {
        <div class="loader">
            <h1>Doing a bit of a Search...</h1>
            <div>
                <img src="/Images/loading.gif" />
            </div>
        </div>
    }
    else
    {
        <HxGrid TItem="LabInvoiceAuditModel" DataProvider="@GetAuditData" PageSize="25">
            <Columns>
                <HxGridColumn @bind-Value="Context.LabAuthorisationId" HeaderText="Id" />
                <HxGridColumn @bind-Value="Context.RecordID" HeaderText="Record ID" />
                <HxGridColumn @bind-Value="Context.OldValue" HeaderText="Old Value" />
                <HxGridColumn @bind-Value="Context.NewValue" HeaderText="New Value" />
                <HxGridColumn @bind-Value="Context.Field" HeaderText="Field" />
                <HxGridColumn @bind-Value="Context.LabCode" HeaderText="Lab Code" />
                <HxGridColumn @bind-Value="Context.LabName" HeaderText="Lab Name" />
                <HxGridColumn @bind-Value="Context.DateTimeUpdated" HeaderText="Date Updated" Format="dd/MM/yyyy" />
                <HxGridColumn @bind-Value="Context.UserUpdated" HeaderText="User" />
            </Columns>
        </HxGrid>
    }

    @code {
        private string RecordId { get; set; }
        private DateTime? StartDateTime { get; set; } = DateTime.Now.AddDays(-7);
        private DateTime? EndDateTime { get; set; } = DateTime.Now;
        private List<LabInvoiceAuditModel> auditInvoices = new List<LabInvoiceAuditModel>();
        private bool isLoading = false;

        private async Task<GridDataProviderResult<LabInvoiceAuditModel>> GetAuditData(GridDataProviderRequest<LabInvoiceAuditModel> request)
        {
            return new GridDataProviderResult<LabInvoiceAuditModel>()
            {
                Data = auditInvoices.Skip(request.StartIndex).Take(request.Count),
                TotalCount = auditInvoices.Count
            };
        }

        private async Task SearchAudits()
        {
            isLoading = true;
            auditInvoices = await LabInvoiceService.SearchForLabAuthorisations(RecordId, StartDateTime, EndDateTime, ""); // UserUpdated will be handled by the service based on current user
            isLoading = false;
            StateHasChanged();
        }
    }
    ```

    **View Toggling:** The original application allowed toggling between the audit report and invoice summary views. In Blazor, this *functionality* will be achieved by providing navigation options (e.g., using Blazor's `<NavLink>` component or programmatic navigation) to switch between the `/labinvoiceauditreport` and `/labinvoicesummary` routes. This ensures users can access both reports as intended.

    **Date Picker Functionality:** The original JavaScript provided specific date picker behaviors. In Blazor, the `datetime-local` input type provides basic date and time selection. To replicate the *exact user experience* of the original date picker (e.g., specific date formats, min/max date constraints, or clearing on keyup), a dedicated Blazor date picker component that offers these features, or targeted JavaScript interop for specific UI behaviors, will be used. The goal is to maintain the *same level of usability* for date input.

### 2.4. Lab Invoice Summary (LabInvoiceSummary.razor)

1.  **Create a new Blazor component** for the Lab Invoice Summary. This component will replace the summary-related logic from `LabInvoicesBindings.js`.

    `PracticeDirectory.Client/Components/Pages/LabInvoices/LabInvoiceSummary.razor`
    ```razor
    @page "/labinvoicesummary"
    @using PracticeDirectory.Client.Shared.Models.LabInvoices
    @using PracticeDirectory.Client.Service
    @inject ILabInvoiceService LabInvoiceService
    @inject IJSRuntime JSRuntime
    @inject IPracticeContextService PracticeContextService
    @inject NavigationManager NavigationManager

    <h3>Unapproved Lab Invoice Summary</h3>

    <div class="formContainer_title-smaller formContainer_title-view">
        <table class="table-searchfilter">
            <tr>
                <td>Practice Query</td>
                <td><input type="text" @bind="PracticeQuery" /></td>
            </tr>
            <tr>
                <td>
                    <button class="btn btn-primary" @onclick="SearchSummary">Search</button>
                </td>
                <td>
                    @if (!string.IsNullOrEmpty(FeedbackMessage))
                    {
                        <div class="alert alert-info">@FeedbackMessage</div>
                    }
                </td>
            </tr>
        </table>
    </div>

    @if (isLoading)
    {
        <div class="loader">
            <h1>Doing a bit of a Search...</h1>
            <div>
                <img src="/Images/loading.gif" />
            </div>
        </div>
    }
    else
    {
        <HxGrid TItem="LabInvoiceSummaryModel" DataProvider="@GetSummaryData" PageSize="25">
            <Columns>
                <HxGridColumn HeaderText="" Sortable="false">
                    <Template>
                        <button class="btn btn-sm btn-info" @onclick="(() => RedirectToPracticeInvoices(Context.PracticeId))">View Invoices</button>
                    </Template>
                </HxGridColumn>
                <HxGridColumn @bind-Value="Context.PracticeId" HeaderText="Practice Number" />
                <HxGridColumn @bind-Value="Context.PracticeName" HeaderText="Practice Name" />
                <HxGridColumn @bind-Value="Context.Count" HeaderText="Count of Unapproved Invoices" />
                <HxGridColumn @bind-Value="Context.InQuery" HeaderText="Number In Query" />
            </Columns>
        </HxGrid>
    }

    @code {
        private string PracticeQuery { get; set; }
        private List<LabInvoiceSummaryModel> summaryInvoices = new List<LabInvoiceSummaryModel>();
        private bool isLoading = false;
        private string FeedbackMessage { get; set; }

        private async Task<GridDataProviderResult<LabInvoiceSummaryModel>> GetSummaryData(GridDataProviderRequest<LabInvoiceSummaryModel> request)
        {
            return new GridDataProviderResult<LabInvoiceSummaryModel>()
            {
                Data = summaryInvoices.Skip(request.StartIndex).Take(request.Count),
                TotalCount = summaryInvoices.Count
            };
        }

        private async Task SearchSummary()
        {
            isLoading = true;
            FeedbackMessage = string.Empty;

            // Replicate the original OData call for practices
            // This would typically involve an API call to a backend service that provides practice data.
            // For exact replication, you might need to create a dedicated API endpoint in the backend
            // that returns a Dictionary<int, string> of practices.
            var allPractices = await PracticeContextService.GetAllPracticesAsDictionary(); // Placeholder for actual implementation

            List<int> filterPractices = null;
            if (!string.IsNullOrEmpty(PracticeQuery))
            {
                // Replicate the original logic for filtering by practice ID or name
                if (int.TryParse(PracticeQuery, out int practiceId))
                {
                    filterPractices = allPractices.Where(p => p.Key.ToString().Contains(PracticeQuery)).Select(p => p.Key).ToList();
                }
                else
                {
                    filterPractices = allPractices.Where(p => p.Value.ToLower().Contains(PracticeQuery.ToLower())).Select(p => p.Key).ToList();
                }
            }

            summaryInvoices = await LabInvoiceService.GetUnapprovedInvoicesCount(allPractices, filterPractices);

            if (summaryInvoices == null || !summaryInvoices.Any())
            {
                FeedbackMessage = $"No Practices were found matching the text {PracticeQuery}";
            }

            isLoading = false;
            StateHasChanged();
        }

        private void RedirectToPracticeInvoices(int practiceId)
        {
            // Replicate the original navigation to Practice/Details/{PracticeId}#1
            NavigationManager.NavigateTo($"/practice/details/{practiceId}#1");
        }
    }
    ```

### 2.4. Lab Invoice Summary (LabInvoiceSummary.razor)

1.  **Create a new Blazor component** for the Lab Invoice Summary. This component will replace the summary-related logic from `LabInvoicesBindings.js`.

    `PracticeDirectory.Client/Components/Pages/LabInvoices/LabInvoiceSummary.razor`
    ```razor
    @page "/labinvoicesummary"
    @using PracticeDirectory.Client.Shared.Models.LabInvoices
    @using PracticeDirectory.Client.Service
    @inject ILabInvoiceService LabInvoiceService
    @inject IJSRuntime JSRuntime
    @inject IPracticeContextService PracticeContextService

    <h3>Unapproved Lab Invoice Summary</h3>

    <div class="formContainer_title-smaller formContainer_title-view">
        <table class="table-searchfilter">
            <tr>
                <td>Practice Query</td>
                <td><input type="text" @bind="PracticeQuery" /></td>
            </tr>
            <tr>
                <td>
                    <button class="btn btn-primary" @onclick="SearchSummary">Search</button>
                </td>
                <td>
                    @if (!string.IsNullOrEmpty(FeedbackMessage))
                    {
                        <div class="alert alert-info">@FeedbackMessage</div>
                    }
                </td>
            </tr>
        </table>
    </div>

    @if (isLoading)
    {
        <div class="loader">
            <h1>Doing a bit of a Search...</h1>
            <div>
                <img src="/Images/loading.gif" />
            </div>
        </div>
    }
    else
    {
        <HxGrid TItem="LabInvoiceSummaryModel" DataProvider="@GetSummaryData" PageSize="25">
            <Columns>
                <HxGridColumn @bind-Value="Context.PracticeId" HeaderText="Practice Number" />
                <HxGridColumn @bind-Value="Context.PracticeName" HeaderText="Practice Name" />
                <HxGridColumn @bind-Value="Context.Count" HeaderText="Count of Unapproved Invoices" />
                <HxGridColumn @bind-Value="Context.InQuery" HeaderText="Number In Query" />
            </Columns>
        </HxGrid>
    }

    @code {
        private string PracticeQuery { get; set; }
        private List<LabInvoiceSummaryModel> summaryInvoices = new List<LabInvoiceSummaryModel>();
        private bool isLoading = false;
        private string FeedbackMessage { get; set; }

        private async Task<GridDataProviderResult<LabInvoiceSummaryModel>> GetSummaryData(GridDataProviderRequest<LabInvoiceSummaryModel> request)
        {
            return new GridDataProviderResult<LabInvoiceSummaryModel>()
            {
                Data = summaryInvoices.Skip(request.StartIndex).Take(request.Count),
                TotalCount = summaryInvoices.Count
            };
        }

        private async Task SearchSummary()
        {
            isLoading = true;
            FeedbackMessage = string.Empty;

            // To replicate the original OData call for practices, the `IPracticeContextService` will be extended
            // with a method (e.g., `GetAllPracticesAsDictionary()`) that retrieves practice data in a `Dictionary<int, string>` format.
            // This will leverage the existing `personItems.MapGet("/", GetAllDirectoryPractices)` API endpoint in the backend.
            // The implementation will involve making an `HttpClient` call from the `PracticeContextService` to this endpoint
            // and then mapping the returned data into the required `Dictionary<int, string>` format.
            var allPractices = await PracticeContextService.GetAllPracticesAsDictionary();

            List<int> filterPractices = null;
            if (!string.IsNullOrEmpty(PracticeQuery))
            {
                // Replicate the original logic for filtering by practice ID or name
                if (int.TryParse(PracticeQuery, out int practiceId))
                {
                    filterPractices = allPractices.Where(p => p.Key.ToString().Contains(PracticeQuery)).Select(p => p.Key).ToList();
                }
                else
                {
                    filterPractices = allPractices.Where(p => p.Value.ToLower().Contains(PracticeQuery.ToLower())).Select(p => p.Key).ToList();
                }
            }

            summaryInvoices = await LabInvoiceService.GetUnapprovedInvoicesCount(allPractices, filterPractices);

            if (summaryInvoices == null || !summaryInvoices.Any())
            {
                FeedbackMessage = $"No Practices were found matching the text {PracticeQuery}";
            }

            isLoading = false;
            StateHasChanged();
        }
    }
    ```

## 3. Helper Classes

1.  **`LabInvoiceMissingException.cs`:** This custom exception is used when a lab invoice is not found.

    `PracticeDirectory.Client/Shared/Helpers/LabInvoiceMissingException.cs`
    ```csharp
    using System;

    namespace PracticeDirectory.Client.Shared.Helpers
    {
        public class LabInvoiceMissingException : Exception
        {
            public LabInvoiceMissingException(string message)
                : base(message)
            {
            }

            public LabInvoiceMissingException(int ledgerEntryId) : base (string.Format("LedgerEntryId {0} has already been approved", ledgerEntryId))
            {
            }
        }
    }
    ```

    *Note: The original `DataRowExtension.cs` is not directly migrated as its functionality is either replaced by Dapper's mapping capabilities or is no longer relevant due to the consolidation of UI components. However, ensure that the Dapper mapping correctly handles `DBNull.Value` and provides default values where necessary, replicating the behavior of `GetColumnValueOrDefault`.*

## 4. Logging

1.  **Implement Logging:** Replace the original `IDHGroup.SharedLibraries.Logging` with a Blazor-compatible logging solution (e.g., `Microsoft.Extensions.Logging`). Ensure debug and error logging is replicated for relevant operations.

## 5. Frontend Considerations

### 5.1. Tooltip Functionality

1.  **Replicate Tooltip Behavior:** The original application used Kendo UI tooltips for grid cells. If similar functionality is desired, a Blazor-compatible tooltip component or custom JavaScript interop will be required to replicate this behavior.

### 5.2. View Toggling (Audit vs. Summary)

1.  **Implement View Switching:** Since the audit report and summary are now separate Blazor components, implement a mechanism for switching between them (e.g., using Blazor's navigation, or a parent component that conditionally renders one or the other).

### 5.3. Date Picker Functionality

1.  **Replicate Date Picker Behavior:** The original JavaScript included specific date picker initialization and keyup events. While `@bind` for `datetime-local` inputs provides basic functionality, any advanced date picker UI/behavior (e.g., specific date formats, min/max dates, clearing on keyup) will require a dedicated Blazor date picker component or custom JavaScript interop.

### 5.4. Practice Data Source for Summary

1.  **Implement `GetAllPracticesAsDictionary`:** The `IPracticeContextService` needs a concrete implementation for `GetAllPracticesAsDictionary()` that retrieves practice data, similar to the original OData call. This might involve a new API endpoint or direct database access.

### 5.5. General Helper Classes

1.  **Encapsulate Common Functionalities:** While `BaseController` and `BaseApiController` are MVC-specific, consider creating Blazor base components or shared services to encapsulate common functionalities like user context retrieval, common error handling, and other cross-cutting concerns that were handled by these base classes in the original project.

## 4. Static Assets

1.  **Copy Image Files:** Copy the following image files from the original project's `Core/Images` directory to `wwwroot/Images` in the new project:
    *   `Clipboard.png`
    *   `BankNote.png`
    *   `loading.gif`

## 5. Permissions

1.  **Implement Authorization Logic:** The `PracticeInvoices.razor` component must implement authorization checks based on the original `LabInvoiceModel.HasPermission` logic. This involves checking the user's roles (ADM, Clarity Super User, PM) and, for PMs, ensuring the `practiceIdToBeAuthorised` matches their assigned practice. The `UserContextService` and `AuthExtensions` should be used for this.

    *   Disable the "Submit", "Check All", "Approve" and "In Query" checkboxes if the user is not authorized to perform actions on the invoices.

    **Implementation Details for Authorization:**
    To replicate the `WisdomUser` functionality and `LabInvoiceModel.HasPermission` logic, the **existing `IUserContextService` will be extended**. This service will encapsulate methods to:
    *   Retrieve the current user's roles (e.g., ADM, Clarity Super User, PM).
    *   Provide the user's location for PM-specific checks (`GetLocationForLabInvoices`).
    *   Provide the user's AD username (`GetAdUsername`) for auditing.
    *   Determine if the user's identity is overridden (`GetIsOverridden`).
    
    The logic for these functionalities, where applicable, will **leverage and be integrated from the existing `AuthExtensions.cs`** into the `UserContextService` implementation. The `PracticeInvoices.razor` component will then inject and utilize this extended `IUserContextService` to perform the necessary permission checks and enable/disable UI elements accordingly, ensuring the *same access control functionality* as the original application.

## 6. General Helper Classes

1.  **Encapsulate Common Functionalities:** While `BaseController` and `BaseApiController` were MVC-specific, their underlying *functionalities* (e.g., user context retrieval, common error handling, logging) will be encapsulated in the Blazor application through shared services and Blazor's built-in features. This ensures a consistent approach to cross-cutting concerns without direct code translation.

## 7. Final Implementation Steps

1.  Add NuGet packages.
2.  Create the Enum and Model files.
3.  Create the `LabInvoiceMissingException.cs` helper class.
4.  Create the `ILabInvoiceService` and its full implementation `LabInvoiceService.cs` (including credit note and audit logic, and replicating `DataRowExtension`'s mapping behavior).
5.  Register the service in `Program.cs`.
6.  Create the `fileDownloader.js` and include it in `App.razor`.
7.  Copy static image assets to `wwwroot/Images`.
8.  Update `PracticeInvoices.razor` with the complete grid UI and logic (including authorization, correct checkbox validation, user feedback, and custom row styling).
9.  Create `LabInvoiceAuditReport.razor` and `LabInvoiceSummary.razor` components.
11. Manually test the UI functionality thoroughly.