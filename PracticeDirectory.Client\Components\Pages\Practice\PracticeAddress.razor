﻿@using PracticeDirectory.Client.Shared.Models.Practices

@if (Address != null)
{
	

	<div class="mb-5">
		@((MarkupString)FormatAddressPart(Address.Address1))
		@((MarkupString)FormatAddressPart(Address.Address2))
		@((MarkupString)FormatAddressPart(Address.Address3))
		@((MarkupString)FormatAddressPart(Address.Address4))
		@((MarkupString)FormatAddressPart(Address.Address5))
		@((MarkupString)FormatAddressPart(Address.Postcode))
		@((MarkupString)FormatAddressPart(Address.City))
		@((MarkupString)FormatAddressPart(Address.Country))
	</div>
}

@code {
	[Parameter]
	public DirectoryPracticePostalAddressModel? Address { get; set; }

	private string FormatAddressPart(string part)
	{
		return !string.IsNullOrEmpty(part) ? $"{part}<br />" : "";
	}
}
