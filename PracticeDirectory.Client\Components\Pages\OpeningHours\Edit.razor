﻿@page "/openinghours/edit"
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.Extensions.Options
@using PracticeDirectory.Client.Components.Pages.ContractedHours
@using PracticeDirectory.Client.Service
@using PracticeDirectory.Client.Shared.Configuration
@using PracticeDirectory.Client.Shared.Extensions
@using PracticeDirectory.Client.Shared.Models.Practices
@inject IHttpClientFactory ClientFactory
@inject PracticeContextService PracticeContext
@inject IOptions<AdminTeamSettings> AdminTeamConfig
@inject UserContextService UserContext
@inject IJSRuntime JSRuntime

@if (showDetail)
{
	<h3 class="mb-4">Opening Hours</h3>
	<div class="text-end mb-2">
		<a href="practice/@PracticeId" class="btn btn-secondary" style="max-width: 240px; line-height: 30px;">
			Back To Practice
		</a>
	</div>
	<div class="row mt-4 mb-4">
		<div class="col-md-12">
			<h5>Contracted Hours</h5>
			<PracticeContractedHours PracticeId="@PracticeId"></PracticeContractedHours>
		</div>
	</div>

	<div class="row">
		<div class="div-wrapper">
			@if (readOnlyViewMode)
			{
				<div class="inner-wrapper-overlay"></div>
			}
			<div class="table-responsive">
				<table class="table-responsive table">

					<thead>
						<tr class="bg-light">
							<th scope="col" width="10%">&nbsp;</th>
							<th scope="col" width="5%"><span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;From</span></th>
							<th scope="col" width="5%"><span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;To</span></th>
							<th class="text-center" scope="col" width="5%">Closed</th>
						</tr>
					</thead>
					<tbody>
						<tr>
							<th scope="row">Monday</th>
							@if (isClosedMonday)
							{
								<td>
									<HxSelect TItem="TimeSlotItemModel"
											  TValue="int?"
											  Label=""
											  Data="timeSlotList"
											  @bind-Value="closedId"
											  style="width: 96px;"
											  TextSelector="@(timeslot => timeslot.Name)"
											  ValueSelector="@(timeslot => timeslot.Id)"
											  Enabled="false"
											  Nullable="false" />
								</td>
								<td>
									<HxSelect TItem="TimeSlotItemModel"
											  TValue="int?"
											  Label=""
											  Data="timeSlotList"
											  @bind-Value="closedId"
											  style="width: 96px;"
											  TextSelector="@(timeslot => timeslot.Name)"
											  ValueSelector="@(timeslot => timeslot.Id)"
											  Enabled="false"
											  Nullable="false" />
								</td>
							}
							else
							{
								<td>
									<HxSelect TItem="TimeSlotItemModel"
											  TValue="int?"
											  Label=""
											  Data="timeSlotList"
											  @bind-Value="mondayOpeningHourId"
											  style="width: 96px;"
											  TextSelector="@(timeslot => timeslot.Name)"
											  ValueSelector="@(timeslot => timeslot.Id)"
											  Nullable="false" />
								</td>
								<td>
									<HxSelect TItem="TimeSlotItemModel"
											  TValue="int?"
											  Label=""
											  Data="timeSlotList"
											  @bind-Value="mondayClosingHourId"
											  style="width: 96px;"
											  TextSelector="@(timeslot => timeslot.Name)"
											  ValueSelector="@(timeslot => timeslot.Id)"
											  Nullable="false" />
								</td>
							}
							<td class="text-center"><HxCheckbox @bind-Value="isClosedMonday" Inline="true" /></td>
						</tr>

						<tr>
							<th scope="row">Tuesday</th>
							@if (isClosedTuesday)
							{
								<td>
									<HxSelect TItem="TimeSlotItemModel"
											  TValue="int?"
											  Label=""
											  Data="timeSlotList"
											  @bind-Value="closedId"
											  style="width: 96px;"
											  TextSelector="@(timeslot => timeslot.Name)"
											  ValueSelector="@(timeslot => timeslot.Id)"
											  Enabled="false"
											  Nullable="false" />
								</td>
								<td>
									<HxSelect TItem="TimeSlotItemModel"
											  TValue="int?"
											  Label=""
											  Data="timeSlotList"
											  @bind-Value="closedId"
											  style="width: 96px;"
											  TextSelector="@(timeslot => timeslot.Name)"
											  ValueSelector="@(timeslot => timeslot.Id)"
											  Enabled="false"
											  Nullable="false" />
								</td>
							}
							else
							{
								<td>
									<HxSelect TItem="TimeSlotItemModel"
											  TValue="int?"
											  Label=""
											  Data="timeSlotList"
											  @bind-Value="tuesdayOpeningHourId"
											  style="width: 96px;"
											  TextSelector="@(timeslot => timeslot.Name)"
											  ValueSelector="@(timeslot => timeslot.Id)"
											  Nullable="false" />
								</td>
								<td>
									<HxSelect TItem="TimeSlotItemModel"
											  TValue="int?"
											  Label=""
											  Data="timeSlotList"
											  @bind-Value="tuesdayClosingHourId"
											  style="width: 96px;"
											  TextSelector="@(timeslot => timeslot.Name)"
											  ValueSelector="@(timeslot => timeslot.Id)"
											  Nullable="false" />
								</td>
							}
							<td class="text-center"><HxCheckbox @bind-Value="isClosedTuesday" Inline="true" /></td>
						</tr>

						<tr>
							<th scope="row">Wednesday</th>
							@if (isClosedWednesday)
							{
								<td>
									<HxSelect TItem="TimeSlotItemModel"
											  TValue="int?"
											  Label=""
											  Data="timeSlotList"
											  @bind-Value="closedId"
											  style="width: 96px;"
											  TextSelector="@(timeslot => timeslot.Name)"
											  ValueSelector="@(timeslot => timeslot.Id)"
											  Enabled="false"
											  Nullable="false" />
								</td>
								<td>
									<HxSelect TItem="TimeSlotItemModel"
											  TValue="int?"
											  Label=""
											  Data="timeSlotList"
											  @bind-Value="closedId"
											  style="width: 96px;"
											  TextSelector="@(timeslot => timeslot.Name)"
											  ValueSelector="@(timeslot => timeslot.Id)"
											  Enabled="false"
											  Nullable="false" />
								</td>
							}
							else
							{
								<td>
									<HxSelect TItem="TimeSlotItemModel"
											  TValue="int?"
											  Label=""
											  Data="timeSlotList"
											  @bind-Value="wednesdayOpeningHourId"
											  style="width: 96px;"
											  TextSelector="@(timeslot => timeslot.Name)"
											  ValueSelector="@(timeslot => timeslot.Id)"
											  Nullable="false" />
								</td>
								<td>
									<HxSelect TItem="TimeSlotItemModel"
											  TValue="int?"
											  Label=""
											  Data="timeSlotList"
											  @bind-Value="wednesdayClosingHourId"
											  style="width: 96px;"
											  TextSelector="@(timeslot => timeslot.Name)"
											  ValueSelector="@(timeslot => timeslot.Id)"
											  Nullable="false" />
								</td>
							}
							<td class="text-center"><HxCheckbox @bind-Value="isClosedWednesday" Inline="true" /></td>
						</tr>

						<tr>
							<th scope="row">Thursday</th>
							@if (isClosedThursday)
							{
								<td>
									<HxSelect TItem="TimeSlotItemModel"
											  TValue="int?"
											  Label=""
											  Data="timeSlotList"
											  @bind-Value="closedId"
											  style="width: 96px;"
											  TextSelector="@(timeslot => timeslot.Name)"
											  ValueSelector="@(timeslot => timeslot.Id)"
											  Enabled="false"
											  Nullable="false" />
								</td>
								<td>
									<HxSelect TItem="TimeSlotItemModel"
											  TValue="int?"
											  Label=""
											  Data="timeSlotList"
											  @bind-Value="closedId"
											  style="width: 96px;"
											  TextSelector="@(timeslot => timeslot.Name)"
											  ValueSelector="@(timeslot => timeslot.Id)"
											  Enabled="false"
											  Nullable="false" />
								</td>
							}
							else
							{
								<td>
									<HxSelect TItem="TimeSlotItemModel"
											  TValue="int?"
											  Label=""
											  Data="timeSlotList"
											  @bind-Value="thursdayOpeningHourId"
											  style="width: 96px;"
											  TextSelector="@(timeslot => timeslot.Name)"
											  ValueSelector="@(timeslot => timeslot.Id)"
											  Nullable="false" />
								</td>
								<td>
									<HxSelect TItem="TimeSlotItemModel"
											  TValue="int?"
											  Label=""
											  Data="timeSlotList"
											  @bind-Value="thursdayClosingHourId"
											  style="width: 96px;"
											  TextSelector="@(timeslot => timeslot.Name)"
											  ValueSelector="@(timeslot => timeslot.Id)"
											  Nullable="false" />
								</td>
							}
							<td class="text-center"><HxCheckbox @bind-Value="isClosedThursday" Inline="true" /></td>
						</tr>

						<tr>
							<th scope="row">Friday</th>
							@if (isClosedFriday)
							{
								<td>
									<HxSelect TItem="TimeSlotItemModel"
											  TValue="int?"
											  Label=""
											  Data="timeSlotList"
											  @bind-Value="closedId"
											  style="width: 96px;"
											  TextSelector="@(timeslot => timeslot.Name)"
											  ValueSelector="@(timeslot => timeslot.Id)"
											  Enabled="false"
											  Nullable="false" />
								</td>
								<td>
									<HxSelect TItem="TimeSlotItemModel"
											  TValue="int?"
											  Label=""
											  Data="timeSlotList"
											  @bind-Value="closedId"
											  style="width: 96px;"
											  TextSelector="@(timeslot => timeslot.Name)"
											  ValueSelector="@(timeslot => timeslot.Id)"
											  Enabled="false"
											  Nullable="false" />
								</td>
							}
							else
							{
								<td>
									<HxSelect TItem="TimeSlotItemModel"
											  TValue="int?"
											  Label=""
											  Data="timeSlotList"
											  @bind-Value="fridayOpeningHourId"
											  style="width: 96px;"
											  TextSelector="@(timeslot => timeslot.Name)"
											  ValueSelector="@(timeslot => timeslot.Id)"
											  Nullable="false" />
								</td>
								<td>
									<HxSelect TItem="TimeSlotItemModel"
											  TValue="int?"
											  Label=""
											  Data="timeSlotList"
											  @bind-Value="fridayClosingHourId"
											  style="width: 96px;"
											  TextSelector="@(timeslot => timeslot.Name)"
											  ValueSelector="@(timeslot => timeslot.Id)"
											  Nullable="false" />
								</td>
							}
							<td class="text-center"><HxCheckbox @bind-Value="isClosedFriday" Inline="true" /></td>
						</tr>

						<tr>
							<th scope="row">Saturday</th>
							@if (isClosedSaturday)
							{
								<td>
									<HxSelect TItem="TimeSlotItemModel"
											  TValue="int?"
											  Label=""
											  Data="timeSlotList"
											  @bind-Value="closedId"
											  style="width: 96px;"
											  TextSelector="@(timeslot => timeslot.Name)"
											  ValueSelector="@(timeslot => timeslot.Id)"
											  Enabled="false"
											  Nullable="false" />
								</td>
								<td>
									<HxSelect TItem="TimeSlotItemModel"
											  TValue="int?"
											  Label=""
											  Data="timeSlotList"
											  @bind-Value="closedId"
											  style="width: 96px;"
											  TextSelector="@(timeslot => timeslot.Name)"
											  ValueSelector="@(timeslot => timeslot.Id)"
											  Enabled="false"
											  Nullable="false" />
								</td>
							}
							else
							{
								<td>
									<HxSelect TItem="TimeSlotItemModel"
											  TValue="int?"
											  Label=""
											  Data="timeSlotList"
											  @bind-Value="saturdayOpeningHourId"
											  style="width: 96px;"
											  TextSelector="@(timeslot => timeslot.Name)"
											  ValueSelector="@(timeslot => timeslot.Id)"
											  Nullable="false" />
								</td>
								<td>
									<HxSelect TItem="TimeSlotItemModel"
											  TValue="int?"
											  Label=""
											  Data="timeSlotList"
											  @bind-Value="saturdayClosingHourId"
											  style="width: 96px;"
											  TextSelector="@(timeslot => timeslot.Name)"
											  ValueSelector="@(timeslot => timeslot.Id)"
											  Nullable="false" />
								</td>
							}
							<td class="text-center"><HxCheckbox @bind-Value="isClosedSaturday" Inline="true" /></td>
						</tr>

						<tr>
							<th scope="row">Sunday</th>
							@if (isClosedSunday)
							{
								<td>
									<HxSelect TItem="TimeSlotItemModel"
											  TValue="int?"
											  Label=""
											  Data="timeSlotList"
											  @bind-Value="closedId"
											  style="width: 96px;"
											  TextSelector="@(timeslot => timeslot.Name)"
											  ValueSelector="@(timeslot => timeslot.Id)"
											  Enabled="false"
											  Nullable="false" />
								</td>
								<td>
									<HxSelect TItem="TimeSlotItemModel"
											  TValue="int?"
											  Label=""
											  Data="timeSlotList"
											  @bind-Value="closedId"
											  style="width: 96px;"
											  TextSelector="@(timeslot => timeslot.Name)"
											  ValueSelector="@(timeslot => timeslot.Id)"
											  Enabled="false"
											  Nullable="false" />
								</td>
							}
							else
							{
								<td>
									<HxSelect TItem="TimeSlotItemModel"
											  TValue="int?"
											  Label=""
											  Data="timeSlotList"
											  @bind-Value="sundayOpeningHourId"
											  style="width: 96px;"
											  TextSelector="@(timeslot => timeslot.Name)"
											  ValueSelector="@(timeslot => timeslot.Id)"
											  Nullable="false" />
								</td>
								<td>
									<HxSelect TItem="TimeSlotItemModel"
											  TValue="int?"
											  Label=""
											  Data="timeSlotList"
											  @bind-Value="sundayClosingHourId"
											  style="width: 96px;"
											  TextSelector="@(timeslot => timeslot.Name)"
											  ValueSelector="@(timeslot => timeslot.Id)"
											  Nullable="false" />
								</td>
							}
							<td class="text-center"><HxCheckbox @bind-Value="isClosedSunday" Inline="true" /></td>
						</tr>
					</tbody>
				</table>
			</div>
		</div>

		<div class="row mt-4 mb-4">
			@if (!readOnlyViewMode)
			{
				<HxButton Text="Save Changes" Color="ThemeColor.Primary" style="max-width: 240px; margin-right: 36px; margin-bottom: 8px;" OnClick="async () => await SaveOpeningHours()" />
			}
			<a href="practice/@PracticeId" class="btn btn-secondary" style="max-width: 240px; margin-bottom: 8px; line-height: 30px;">Back To Practice</a>
		</div>

		@if (showInProgressWarningMessage)
		{
			<div class="alert alert-warning" role="alert">
				Requested Opening Hours changes are in progress. This can take up to 24 hours.
			</div>
		}

		@if (showErrorMessage)
		{
			<div class="alert alert-danger" role="alert">
				@lastErrorMessage
			</div>
		}

		@if (showSuccessMessage)
		{
			<div class="alert alert-success" role="alert">
				@lastSuccessMessage
			</div>
		}
		<hr class="my-5" />

		<h3 class="mb-4">Manage Opening Hours Exceptions</h3>
		<OpeningHourExceptions PracticeId="@PracticeId" CanEdit="@(!readOnlyViewMode)" />
	</div>
}

@code {
	[SupplyParameterFromQuery]
	private int PracticeId { get; set; }

	[CascadingParameter]
	private Task<AuthenticationState> authenticationStateTask { get; set; } = default!;
	private string userName = string.Empty;
	private string isAuthenticated = string.Empty;

	private DirectoryPracticeOpeningHoursModel openingHoursModel = new();
	private bool showInProgressWarningMessage = false;
	private bool showErrorMessage = false;
	private string lastErrorMessage = string.Empty;
	private bool showSuccessMessage = false;
	private string lastSuccessMessage = string.Empty;
	private List<TimeSlotItemModel> timeSlotList = [];
	Dictionary<int, string> timeSlotDict = new();
	private bool readOnlyViewMode = true;
	private bool isPracticeManager { get; set; } = false;
	private int currentUserPracticeId { get; set; } = 0;
	private string originalUser = string.Empty;
	private bool isAdmin = false;
	private bool showDetail = false;

	private static readonly int timeslot_9AM = 109;
	private static readonly int timeslot_6PM = 217;

	bool isClosedMonday, isClosedTuesday, isClosedWednesday, isClosedThursday, isClosedFriday, isClosedSaturday, isClosedSunday = true;
	bool origIsClosedMonday, origIsClosedTuesday, origIsClosedWednesday, origIsClosedThursday, origIsClosedFriday, origIsClosedSaturday, origIsClosedSunday = true;

	private int? closedId = null;
	private int? mondayOpeningHourId = timeslot_9AM;
	private int? mondayClosingHourId = timeslot_6PM;
	private int? tuesdayOpeningHourId = timeslot_9AM;
	private int? tuesdayClosingHourId = timeslot_6PM;
	private int? wednesdayOpeningHourId = timeslot_9AM;
	private int? wednesdayClosingHourId = timeslot_6PM;
	private int? thursdayOpeningHourId = timeslot_9AM;
	private int? thursdayClosingHourId = timeslot_6PM;
	private int? fridayOpeningHourId = timeslot_9AM;
	private int? fridayClosingHourId = timeslot_6PM;
	private int? saturdayOpeningHourId = timeslot_9AM;
	private int? saturdayClosingHourId = timeslot_6PM;
	private int? sundayOpeningHourId = timeslot_9AM;
	private int? sundayClosingHourId = timeslot_6PM;

	private int? origMondayOpeningHourId = timeslot_9AM;
	private int? origMondayClosingHourId = timeslot_6PM;
	private int? origTuesdayOpeningHourId = timeslot_9AM;
	private int? origTuesdayClosingHourId = timeslot_6PM;
	private int? origWednesdayOpeningHourId = timeslot_9AM;
	private int? origWednesdayClosingHourId = timeslot_6PM;
	private int? origThursdayOpeningHourId = timeslot_9AM;
	private int? origThursdayClosingHourId = timeslot_6PM;
	private int? origFridayOpeningHourId = timeslot_9AM;
	private int? origFridayClosingHourId = timeslot_6PM;
	private int? origSaturdayOpeningHourId = timeslot_9AM;
	private int? origSaturdayClosingHourId = timeslot_6PM;
	private int? origSundayOpeningHourId = timeslot_9AM;
	private int? origSundayClosingHourId = timeslot_6PM;

	protected override async Task OnInitializedAsync()
	{
		showDetail = false;
		try
		{
			GetOpeningTimeSlotsAsync();
			await GetOpeningHoursAsync();

			if (string.IsNullOrWhiteSpace(UserContext.OverrideUsername))
			{
				var storedOverride = await JSRuntime.InvokeAsync<string>("localStorage.getItem", "overrideUsername");
				if (!string.IsNullOrWhiteSpace(storedOverride))
				{
					UserContext.OverrideUsername = storedOverride;
				}
			}

			var authState = await authenticationStateTask;
			userName = authState.GetWindowsUserName(UserContext.OverrideUsername);
			isAuthenticated = authState.GetWindowsUserIsAuthenticated().ToYesNo();

			currentUserPracticeId = authState.GetPracticeId(ClientFactory, UserContext.OverrideUsername);
			isPracticeManager = await GetCurrentPMAsync(PracticeId, currentUserPracticeId);
			originalUser = authState.GetOriginalWindowsUserName();
			isAdmin = AuthExtensions.IsUserAdmin(originalUser);

			// if user is not a practice manager then do not allow editing
			readOnlyViewMode = !isPracticeManager;

			// if administrator then always allow editing
			if (readOnlyViewMode && isAdmin) { readOnlyViewMode = false; }
		}
		finally
		{
			showDetail = true;
		}
	}

	private async Task<bool> GetCurrentPMAsync(int ParentPractice, int CurrentPractice)
	{
		if (ParentPractice != 0 && CurrentPractice != 0)
		{
			return ParentPractice == CurrentPractice;
		}

		await Task.CompletedTask;
		return false;
	}

	private async Task SaveOpeningHours()
	{
		showSuccessMessage = false;
		showErrorMessage = false;

		if (CheckModelIsValid())
		{
			var dtoToSend = ConvertOpeningHoursToDto();
			await SaveOpeningHoursDto(dtoToSend);

			if (showErrorMessage == false)
			{
				lastSuccessMessage = "Saved Successfully";
				showErrorMessage = false;
				showSuccessMessage = true;
				StateHasChanged();
				await Task.Delay(800);
				showSuccessMessage = false;
				StateHasChanged();
				showInProgressWarningMessage = true;
				StateHasChanged();
			}
		}
		else
		{
			showSuccessMessage = false;
			showErrorMessage = true;
		}
	}

	private bool CheckModelIsValid()
	{
		if (isClosedMonday && isClosedTuesday && isClosedWednesday && isClosedThursday && isClosedFriday && isClosedSaturday && isClosedSunday)
		{
			lastErrorMessage = "Being closed for all seven days is not permitted";
			return false;
		}

		if (!ValidOpeningAndClosingHours()) { return false; }

		if (origIsClosedMonday == isClosedMonday && origIsClosedTuesday == isClosedTuesday && origIsClosedWednesday == isClosedWednesday &&
			origIsClosedThursday == isClosedThursday && origIsClosedFriday == isClosedFriday &&
			origIsClosedSaturday == isClosedSaturday && origIsClosedSunday == isClosedSunday && !TimeSlotsHaveChanged())
		{
			lastErrorMessage = "No changes have been made";
			return false;
		}

		return true;
	}

	private bool ValidOpeningAndClosingHours()
	{
		// Closing hours have to be greater than Opening Hours for each day

		if (mondayOpeningHourId >= mondayClosingHourId)
		{
			lastErrorMessage = "Monday Opening and Closing Times are invalid. (Closing Time must be greater than the Opening Time)";
			return false;
		}

		if (tuesdayOpeningHourId >= tuesdayClosingHourId)
		{
			lastErrorMessage = "Tuesday Opening and Closing Times are invalid. (Closing Time must be greater than the Opening Time)";
			return false;
		}

		if (wednesdayOpeningHourId >= wednesdayClosingHourId)
		{
			lastErrorMessage = "Wednesday Opening and Closing Times are invalid. (Closing Time must be greater than the Opening Time)";
			return false;
		}

		if (thursdayOpeningHourId >= thursdayClosingHourId)
		{
			lastErrorMessage = "Thursday Opening and Closing Times are invalid. (Closing Time must be greater than the Opening Time)";
			return false;
		}

		if (fridayOpeningHourId >= fridayClosingHourId)
		{
			lastErrorMessage = "Friday Opening and Closing Times are invalid. (Closing Time must be greater than the Opening Time)";
			return false;
		}

		if (saturdayOpeningHourId >= saturdayClosingHourId)
		{
			lastErrorMessage = "Saturday Opening and Closing Times are invalid. (Closing Time must be greater than the Opening Time)";
			return false;
		}

		if (sundayOpeningHourId >= sundayClosingHourId)
		{
			lastErrorMessage = "Sunday Opening and Closing Times are invalid. (Closing Time must be greater than the Opening Time)";
			return false;
		}

		return true;
	}

	private bool TimeSlotsHaveChanged()
	{
		if (!isClosedMonday)
		{
			if (origMondayOpeningHourId != mondayOpeningHourId) { return true; }
			if (origMondayClosingHourId != mondayClosingHourId) { return true; }
		}

		if (!isClosedTuesday)
		{
			if (origTuesdayOpeningHourId != tuesdayOpeningHourId) { return true; }
			if (origTuesdayClosingHourId != tuesdayClosingHourId) { return true; }
		}

		if (!isClosedWednesday)
		{
			if (origWednesdayOpeningHourId != wednesdayOpeningHourId) { return true; }
			if (origWednesdayClosingHourId != wednesdayClosingHourId) { return true; }
		}

		if (!isClosedThursday)
		{
			if (origThursdayOpeningHourId != thursdayOpeningHourId) { return true; }
			if (origThursdayClosingHourId != thursdayClosingHourId) { return true; }
		}

		if (!isClosedFriday)
		{
			if (origFridayOpeningHourId != fridayOpeningHourId) { return true; }
			if (origFridayClosingHourId != fridayClosingHourId) { return true; }
		}

		if (!isClosedSaturday)
		{
			if (origSaturdayOpeningHourId != saturdayOpeningHourId) { return true; }
			if (origSaturdayClosingHourId != saturdayClosingHourId) { return true; }
		}

		if (!isClosedSunday)
		{
			if (origSundayOpeningHourId != sundayOpeningHourId) { return true; }
			if (origSundayClosingHourId != sundayClosingHourId) { return true; }
		}

		return false;
	}

	private async Task SaveOpeningHoursDto(DirectoryPracticeOpeningHoursDto dto)
	{
		// Name of client is critical for Windows Auth
		var httpClient = ClientFactory.CreateClient(ApiSettings.Api);
		httpClient.DefaultRequestHeaders.Add("X-ORIG-NAME", userName);
		using HttpResponseMessage response = await httpClient.PostAsJsonAsync($"practiceopeninghours", dto);

		try
		{
			response.EnsureSuccessStatusCode();
		}
		catch (Exception)
		{
			// assume it is a read only user
			lastErrorMessage = "Changes were not saved";
			showErrorMessage = true;
		}
	}

	private void GetOpeningTimeSlotsAsync()
	{
		timeSlotList = TimeSlotsForPractice.GetTimeSlots();
		timeSlotDict = timeSlotList.ToDictionary(_ => _.Id, _ => _.Name);
	}

	private async Task GetOpeningHoursAsync()
	{
		// Name of client is critical for Windows Auth
		var httpClient = ClientFactory.CreateClient(ApiSettings.Api);
		httpClient.DefaultRequestHeaders.Add("X-ORIG-NAME", userName);

		try
		{
			showInProgressWarningMessage = false;
			showErrorMessage = false;

			var today = DateTime.Today;
			DateTime weekEndingDate = today.AddDays(-(int)today.DayOfWeek).AddDays(7);
			string formattedWeekEndingDate = weekEndingDate.ToString("yyyy-MM-dd");
			var response = await httpClient.GetFromJsonAsync<DirectoryPracticeOpeningHoursInfoFullModel>($"practiceopeninghours/{PracticeId}?weekEndDate={formattedWeekEndingDate}");
			var practiceOpeningHoursInfoList = response?.Records.OrderBy(r => r.Day_Of_Week).ToList() ?? [];

			if (practiceOpeningHoursInfoList.Count() > 0)
			{
				if (practiceOpeningHoursInfoList[0].In_Progress)
				{
					showInProgressWarningMessage = true;
				}
			}

			openingHoursModel = ConvertInfoModelToOpeningHoursModel(practiceOpeningHoursInfoList);
			ConvertOpeningHoursModelToInidividualVariables();
		}
		catch (Exception ex)
		{
			Console.WriteLine(ex.Message);
			showErrorMessage = true;
			StateHasChanged();
			await Task.Delay(2000);
			showErrorMessage = false;
			StateHasChanged();
		}
	}

	private void ConvertOpeningHoursModelToInidividualVariables()
	{
		isClosedMonday = openingHoursModel.IsClosed[1];
		origIsClosedMonday = openingHoursModel.IsClosed[1];
		isClosedTuesday = openingHoursModel.IsClosed[2];
		origIsClosedTuesday = openingHoursModel.IsClosed[2];
		isClosedWednesday = openingHoursModel.IsClosed[3];
		origIsClosedWednesday = openingHoursModel.IsClosed[3];
		isClosedThursday = openingHoursModel.IsClosed[4];
		origIsClosedThursday = openingHoursModel.IsClosed[4];
		isClosedFriday = openingHoursModel.IsClosed[5];
		origIsClosedFriday = openingHoursModel.IsClosed[5];
		isClosedSaturday = openingHoursModel.IsClosed[6];
		origIsClosedSaturday = openingHoursModel.IsClosed[6];
		isClosedSunday = openingHoursModel.IsClosed[7];
		origIsClosedSunday = openingHoursModel.IsClosed[7];

		mondayOpeningHourId = GetTimeSlotIdFromName(openingHoursModel.OpeningHours[1], timeslot_9AM);
		origMondayOpeningHourId = GetTimeSlotIdFromName(openingHoursModel.OpeningHours[1], timeslot_9AM);
		mondayClosingHourId = GetTimeSlotIdFromName(openingHoursModel.ClosingHours[1], timeslot_6PM);
		origMondayClosingHourId = GetTimeSlotIdFromName(openingHoursModel.ClosingHours[1], timeslot_6PM);

		tuesdayOpeningHourId = GetTimeSlotIdFromName(openingHoursModel.OpeningHours[2], timeslot_9AM);
		origTuesdayOpeningHourId = GetTimeSlotIdFromName(openingHoursModel.OpeningHours[2], timeslot_9AM);
		tuesdayClosingHourId = GetTimeSlotIdFromName(openingHoursModel.ClosingHours[2], timeslot_6PM);
		origTuesdayClosingHourId = GetTimeSlotIdFromName(openingHoursModel.ClosingHours[2], timeslot_6PM);

		wednesdayOpeningHourId = GetTimeSlotIdFromName(openingHoursModel.OpeningHours[3], timeslot_9AM);
		origWednesdayOpeningHourId = GetTimeSlotIdFromName(openingHoursModel.OpeningHours[3], timeslot_9AM);
		wednesdayClosingHourId = GetTimeSlotIdFromName(openingHoursModel.ClosingHours[3], timeslot_6PM);
		origWednesdayClosingHourId = GetTimeSlotIdFromName(openingHoursModel.ClosingHours[3], timeslot_6PM);

		thursdayOpeningHourId = GetTimeSlotIdFromName(openingHoursModel.OpeningHours[4], timeslot_9AM);
		origThursdayOpeningHourId = GetTimeSlotIdFromName(openingHoursModel.OpeningHours[4], timeslot_9AM);
		thursdayClosingHourId = GetTimeSlotIdFromName(openingHoursModel.ClosingHours[4], timeslot_6PM);
		origThursdayClosingHourId = GetTimeSlotIdFromName(openingHoursModel.ClosingHours[4], timeslot_6PM);

		fridayOpeningHourId = GetTimeSlotIdFromName(openingHoursModel.OpeningHours[5], timeslot_9AM);
		origFridayOpeningHourId = GetTimeSlotIdFromName(openingHoursModel.OpeningHours[5], timeslot_9AM);
		fridayClosingHourId = GetTimeSlotIdFromName(openingHoursModel.ClosingHours[5], timeslot_6PM);
		origFridayClosingHourId = GetTimeSlotIdFromName(openingHoursModel.ClosingHours[5], timeslot_6PM);

		saturdayOpeningHourId = GetTimeSlotIdFromName(openingHoursModel.OpeningHours[6], timeslot_9AM);
		origSaturdayOpeningHourId = GetTimeSlotIdFromName(openingHoursModel.OpeningHours[6], timeslot_9AM);
		saturdayClosingHourId = GetTimeSlotIdFromName(openingHoursModel.ClosingHours[6], timeslot_6PM);
		origSaturdayClosingHourId = GetTimeSlotIdFromName(openingHoursModel.ClosingHours[6], timeslot_6PM);

		sundayOpeningHourId = GetTimeSlotIdFromName(openingHoursModel.OpeningHours[7], timeslot_9AM);
		origSundayOpeningHourId = GetTimeSlotIdFromName(openingHoursModel.OpeningHours[7], timeslot_9AM);
		sundayClosingHourId = GetTimeSlotIdFromName(openingHoursModel.ClosingHours[7], timeslot_6PM);
		origSundayClosingHourId = GetTimeSlotIdFromName(openingHoursModel.ClosingHours[7], timeslot_6PM);
	}



	private DirectoryPracticeOpeningHoursDto ConvertOpeningHoursToDto()
	{
		DirectoryPracticeOpeningHoursDto dto = new();

		var val = timeSlotDict[(mondayOpeningHourId ?? 0)];

		// default all days to closed, including invalid row 0
		for (int i = 0; i < 8; i++)
		{
			dto.IsClosed[i] = true;
		}

		dto.PracticeId = PracticeId;
		if (!isClosedMonday)
		{
			dto.OpeningHours[1] = timeSlotDict[(mondayOpeningHourId ?? 0)];
			dto.ClosingHours[1] = timeSlotDict[(mondayClosingHourId ?? 0)];
			dto.IsClosed[1] = false;
			dto.LastUpdatedBy = userName;
			dto.LastUpdateDate = DateTime.Now;
		}

		if (!isClosedTuesday)
		{
			dto.OpeningHours[2] = timeSlotDict[(tuesdayOpeningHourId ?? 0)];
			dto.ClosingHours[2] = timeSlotDict[(tuesdayClosingHourId ?? 0)];
			dto.IsClosed[2] = false;
			dto.LastUpdatedBy = userName;
			dto.LastUpdateDate = DateTime.Now;
		}

		if (!isClosedWednesday)
		{
			dto.OpeningHours[3] = timeSlotDict[(wednesdayOpeningHourId ?? 0)];
			dto.ClosingHours[3] = timeSlotDict[(wednesdayClosingHourId ?? 0)];
			dto.IsClosed[3] = false;
			dto.LastUpdatedBy = userName;
			dto.LastUpdateDate = DateTime.Now;
		}

		if (!isClosedThursday)
		{
			dto.OpeningHours[4] = timeSlotDict[(thursdayOpeningHourId ?? 0)];
			dto.ClosingHours[4] = timeSlotDict[(thursdayClosingHourId ?? 0)];
			dto.IsClosed[4] = false;
			dto.LastUpdatedBy = userName;
			dto.LastUpdateDate = DateTime.Now;
		}

		if (!isClosedFriday)
		{
			dto.OpeningHours[5] = timeSlotDict[(fridayOpeningHourId ?? 0)];
			dto.ClosingHours[5] = timeSlotDict[(fridayClosingHourId ?? 0)];
			dto.IsClosed[5] = false;
			dto.LastUpdatedBy = userName;
			dto.LastUpdateDate = DateTime.Now;
		}

		if (!isClosedSaturday)
		{
			dto.OpeningHours[6] = timeSlotDict[(saturdayOpeningHourId ?? 0)];
			dto.ClosingHours[6] = timeSlotDict[(saturdayClosingHourId ?? 0)];
			dto.IsClosed[6] = false;
			dto.LastUpdatedBy = userName;
			dto.LastUpdateDate = DateTime.Now;
		}

		if (!isClosedSunday)
		{
			dto.OpeningHours[7] = timeSlotDict[(sundayOpeningHourId ?? 0)];
			dto.ClosingHours[7] = timeSlotDict[(sundayClosingHourId ?? 0)];
			dto.IsClosed[7] = false;
			dto.LastUpdatedBy = userName;
			dto.LastUpdateDate = DateTime.Now;
		}

		return dto;
	}

	private int GetTimeSlotIdFromName(string slotName, int defaultVal)
	{
		if (slotName == "CLOSED") { return defaultVal; }
		int key = timeSlotDict.FirstOrDefault(x => x.Value == slotName).Key;
		if (key == 0) { return defaultVal; }
		return key;
	}

	private DirectoryPracticeOpeningHoursModel ConvertInfoModelToOpeningHoursModel(List<DirectoryPracticeOpeningHoursInfoModel> practiceOpeningHoursInfoList)
	{
		DirectoryPracticeOpeningHoursModel res = new();
		for (int i = 1; i < 8; i++)
		{
			var item = practiceOpeningHoursInfoList.Where(_ => _.Day_Of_Week == i).FirstOrDefault();

			if (item == null)
			{
				res.IsClosed[i] = true;
				res.OpeningHours[i] = "CLOSED";
				res.ClosingHours[i] = "CLOSED";
			}
			else
			{
				if (item.Is_Closed == 1)
				{
					res.IsClosed[i] = true;
					res.OpeningHours[i] = "CLOSED";
					res.ClosingHours[i] = "CLOSED";
				}
				else
				{
					res.IsClosed[i] = false;
					if (!string.IsNullOrEmpty(item.Opening_Time_Name))
					{
						res.OpeningHours[i] = item.Opening_Time_Name;
					}

					if (!string.IsNullOrEmpty(item.Closing_Time_Name))
					{
						res.ClosingHours[i] = item.Closing_Time_Name;
					}
				}
			}
		}

		res.ImportStatus = "Processing";
		res.PracticeId = PracticeId;

		return res;
	}
}
