﻿@using Microsoft.AspNetCore.Components.Authorization
@using PracticeDirectory.Client.Shared.Configuration
@using PracticeDirectory.Client.Shared.Extensions
@using PracticeDirectory.Client.Shared.Models
@using PracticeDirectory.Client.Service
@using PracticeDirectory.Client.Shared.Models.Practices
@inject IHttpClientFactory ClientFactory
@inject UserContextService UserContext
@inject PracticeContextService PracticeContext
@inject IJSRuntime JSRuntime
@inject NavigationManager Navigation

<h3>Practice Team</h3>

@if (loading)
{
    <p><em>Loading...</em></p>
}
else if (loadError)
{
    <p class="text-danger">Error loading team data.</p>
}
else
{
    @if (RegionPeople?.Any() == true)
    {
        <div class="row">
            @foreach (var person in RegionPeople)
            {
                <div class="col-md-4 mb-4">
                    <div class="team-card card h-100 border-0 shadow-sm">
                        <div class="card-body d-flex align-items-start">
                            <div class="avatar bg-primary text-white me-3">
                                <i class="bi bi-person-fill"></i>
                            </div>
                            <div>
                                <h6 class="card-subtitle text-muted mb-1">@person.RoleName</h6>
                                <h5 class="card-title mb-0">@person.PersonName</h5>
                                <p class="mb-1 text-body">@person.EmailAddress</p>
                                <p class="mb-0 text-body">@person.PhoneNumber</p>
                            </div>
                        </div>
                    </div>
                </div>
            }
        </div>
    }

    @if (AreaPeople?.Any() == true)
    {
        <div class="row">
            @foreach (var person in AreaPeople)
            {
                <div class="col-md-4 mb-4">
                    <div class="team-card card h-100 border-0 shadow-sm">
                        <div class="card-body d-flex align-items-start">
                            <div class="avatar bg-primary text-white me-3">
                                <i class="bi bi-person-fill"></i>
                            </div>
                            <div>
                                <h6 class="card-subtitle text-muted mb-1">@person.RoleName</h6>
                                <h5 class="card-title mb-0">@person.PersonName</h5>
                                <p class="mb-1 text-body">@person.EmailAddress</p>
                                <p class="mb-0 text-body">@person.PhoneNumber</p>
                            </div>
                        </div>
                    </div>
                </div>
            }
        </div>
    }
    @if (PracticePeople?.Any() == true)
    {
        <div class="row">
            @foreach (var person in PracticePeople)
            {
                <div class="col-md-4 mb-4">
                    <div class="team-card card h-100 border-0 shadow-sm">
                        <div class="card-body d-flex align-items-start">
                            <div class="avatar bg-primary text-white me-3">
                                <i class="bi bi-person-fill"></i>
                            </div>
                            <div>
                                <h6 class="card-subtitle text-muted mb-1">@person.PositionDescription</h6>
                                <h5 class="card-title mb-0">@person.FullName</h5>
                                <p class="mb-1 text-body">@person.EmailAddress</p>
                                <p class="mb-0 text-body">@person.PhoneNumber</p>
                            </div>
                        </div>
                    </div>
                </div>
            }
        </div>
    }
}

@code {
    [Parameter]
    public DirectoryPracticeModel Practice { get; set; } = new();

    private List<PracticeAreaPerson> AreaPeople = new();
    private List<PracticeRegionPerson> RegionPeople = new();
    private List<PracticeTeam> PracticePeople = new();

    private bool loadError = false;    
    private string userName = string.Empty;
    private string isAuthenticated = string.Empty;
    private bool loading = true;


    [CascadingParameter]
    private Task<AuthenticationState> authenticationStateTask { get; set; } = default!;

    protected override async Task OnInitializedAsync()
    {
        await LoadOverrideUsernameAsync();

        var authState = await authenticationStateTask;
        userName = authState.GetWindowsUserName(UserContext.OverrideUsername);
        isAuthenticated = authState.GetWindowsUserIsAuthenticated().ToYesNo();
        await LoadTeamDataAsync();
    }

    private async Task LoadOverrideUsernameAsync()
    {
        if (string.IsNullOrWhiteSpace(UserContext.OverrideUsername))
        {
            var storedOverride = await JSRuntime.InvokeAsync<string>("localStorage.getItem", "overrideUsername");
            if (!string.IsNullOrWhiteSpace(storedOverride))
            {
                UserContext.OverrideUsername = storedOverride;
            }
        }
    }

    private async Task LoadTeamDataAsync()
    {
        try
        {
            var client = ClientFactory.CreateClient(ApiSettings.Api);
            client.DefaultRequestHeaders.Add("X-ORIG-NAME", userName);

            // Start all GET requests in parallel
            var areaResponseTask = client.GetAsync($"practiceareaperson/{Practice.Id}");
            var regionResponseTask = client.GetAsync($"practiceregionperson/{Practice.Id}");
            var practiceResponseTask = client.GetAsync($"practiceTeam/{Practice.Id}");

            await Task.WhenAll(areaResponseTask, regionResponseTask, practiceResponseTask);

            // Handle Area
            if (areaResponseTask.Result.IsSuccessStatusCode)
            {
                AreaPeople = await areaResponseTask.Result.Content.ReadFromJsonAsync<List<PracticeAreaPerson>>() ?? new();
            }
            else if (areaResponseTask.Result.StatusCode == System.Net.HttpStatusCode.NotFound)
            {
                AreaPeople = new(); // No area people found
            }
            else
            {
                throw new HttpRequestException($"Failed to fetch area people: {areaResponseTask.Result.StatusCode}");
            }

            // Handle Region
            if (regionResponseTask.Result.IsSuccessStatusCode)
            {
                RegionPeople = await regionResponseTask.Result.Content.ReadFromJsonAsync<List<PracticeRegionPerson>>() ?? new();
            }
            else if (regionResponseTask.Result.StatusCode == System.Net.HttpStatusCode.NotFound)
            {
                RegionPeople = new(); // No region people found
            }
            else
            {
                throw new HttpRequestException($"Failed to fetch region people: {regionResponseTask.Result.StatusCode}");
            }

            // Handle Practice
            if (practiceResponseTask.Result.IsSuccessStatusCode)
            {
                PracticePeople = await practiceResponseTask.Result.Content.ReadFromJsonAsync<List<PracticeTeam>>() ?? new();
            }
            else if (practiceResponseTask.Result.StatusCode == System.Net.HttpStatusCode.NotFound)
            {
                PracticePeople = new(); // No practice team found
            }
            else
            {
                throw new HttpRequestException($"Failed to fetch practice team: {practiceResponseTask.Result.StatusCode}");
            }
        }
        catch
        {
            loadError = true;
        }
        finally
        {
            loading = false;
        }
    }


}
