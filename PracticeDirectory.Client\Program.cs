using Havit.Blazor.Components.Web;
using Havit.Blazor.Components.Web.Bootstrap;
using Microsoft.AspNetCore.Authentication.Negotiate;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.Extensions.Options;
using PracticeDirectory.Client.Components;
using PracticeDirectory.Client.Service;
using PracticeDirectory.Client.Shared.Configuration;
using PracticeDirectory.Client.Shared.Extensions;
using PracticeDirectory.Client.Shared.Providers;

var builder = WebApplication.CreateBuilder(args);

builder.Services.AddRazorComponents()
	.AddInteractiveServerComponents();

builder.Services.AddHxServices();
builder.Services.AddHxMessenger();
builder.Services.AddHxMessageBoxHost();

builder.Services.Configure<ImpersonationSettings>(builder.Configuration.GetSection("Impersonation"));
builder.Services.Configure<CommissioningTeamSettings>(builder.Configuration.GetSection("CommissioningTeamUsers"));
builder.Services.Configure<MarketingTeamSettings>(builder.Configuration.GetSection("MarketingTeamUsers"));
builder.Services.Configure<AdminTeamSettings>(builder.Configuration.GetSection("AdminTeamUsers"));
builder.Services.Configure<FileStorageHubSettings>(builder.Configuration.GetSection("FileStorageHub"));
builder.Services.Configure<PhoneNumberUsersSettings>(builder.Configuration.GetSection("PhoneNumberUsers"));
builder.Services.Configure<CustomAttributesSettings>(builder.Configuration.GetSection("CustomAttributesSettings"));
builder.Services.Configure<ApiSettings>(builder.Configuration.GetSection("Api"));

// Windows Auth
builder.Services.AddAuthentication(NegotiateDefaults.AuthenticationScheme).AddNegotiate();

builder.Services.AddAuthorization(options =>
{
	options.FallbackPolicy = options.DefaultPolicy;
});
builder.Services.AddCascadingAuthenticationState();
builder.Services.AddScoped<AuthenticationStateProvider, CustomAuthenticationStateProvider>();
builder.Services.AddScoped<PracticeContextService>();
builder.Services.AddScoped<IFileStorageHubService, FileStorageHubService>();
builder.Services.AddScoped<UserContextService>();

builder.Services.AddHttpContextAccessor();

var apiSettings = new ApiSettings();
builder.Configuration.GetSection(ApiSettings.Api).Bind(apiSettings);
 
builder.Services.AddHttpClient(ApiSettings.Api, c =>
{
    c.BaseAddress = new Uri(apiSettings.BackendUrl);
})
.ConfigurePrimaryHttpMessageHandler(() =>
{
	return new HttpClientHandler()
	{
		UseDefaultCredentials = true
	};
});
var gdcapiSettings = new GDCApiSettings();
builder.Configuration.GetSection(GDCApiSettings.Api).Bind(gdcapiSettings);

builder.Services.AddHttpClient(GDCApiSettings.Api, c =>
{
    c.BaseAddress = new Uri(gdcapiSettings.GDCApiUrl);
})
.ConfigurePrimaryHttpMessageHandler(() =>
{
    return new HttpClientHandler()
    {
        UseDefaultCredentials = true
    };
});


// Add HTTP client for FileStorageHub
var fileStorageHubSettings = new FileStorageHubSettings();
builder.Configuration.GetSection("FileStorageHub").Bind(fileStorageHubSettings);

builder.Services.AddHttpClient<IFileStorageHubService, FileStorageHubService>(c =>
{
	c.BaseAddress = new Uri(fileStorageHubSettings.URL);
})
.ConfigurePrimaryHttpMessageHandler(() =>
{
	return new HttpClientHandler()
	{
		UseDefaultCredentials = true
	};
});

var app = builder.Build();

var impersonationSettings = app.Services.GetRequiredService<IOptions<ImpersonationSettings>>().Value;
var commissioningTeamSettings = app.Services.GetRequiredService<IOptions<CommissioningTeamSettings>>().Value;
var marketingTeamSettings = app.Services.GetRequiredService<IOptions<MarketingTeamSettings>>().Value;
var adminTeamSettings = app.Services.GetRequiredService<IOptions<AdminTeamSettings>>().Value;
var customAttributesSettings = app.Services.GetRequiredService<IOptions<CustomAttributesSettings>>().Value;
AuthExtensions.InitializeAuthSettings(impersonationSettings, commissioningTeamSettings, marketingTeamSettings, adminTeamSettings, customAttributesSettings);

// Configure the HTTP request pipeline.
if (!app.Environment.IsDevelopment())
{
	app.UseExceptionHandler("/Error", createScopeForErrors: true);
	// The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
	app.UseHsts();
}

app.UseHttpsRedirection();

app.UseStaticFiles();
app.UseAntiforgery();

app.MapRazorComponents<App>()
	.AddInteractiveServerRenderMode();

app.Run();
