{"Api": {"BackendUrl": "http://dev-myd-webnew/PracticeDirectory.Api.V1.Dev/"}, "GDCApi": {"GDCApiUrl": "http://myd-web-new/Person.API.v2/Clinician/CheckGdcRegistration"}, "FileStorageHub": {"URL": "https://apinew.mydentist.co.uk/FileStorageHub.Api.V2/api", "APIKey": "6OuOvkdfgOtBGlq75VhLEch5zfA5dV1IJmPm", "ImageUploadEndpointPath": "/Image/upload", "ImageDownloadEndpointPath": "/Image/download"}, "CommissioningTeamUsers": {"CommissioningTeamUserNames": "lnoi|hcranston|mark.price|vtoner|cpage|wmeakin|richard.lee|atandon|gberesford|cprathapan|ahemingway|efuchs|mhosni|pmathew"}, "MarketingTeamUsers": {"MarketingTeamUserNames": "mhosni|e<PERSON><PERSON>|ah<PERSON>ing<PERSON>|cprathapan|gberesford|atandon|richard.lee|pmathew"}, "AdminTeamUsers": {"AdminTeamUsersNames": "mhosni|e<PERSON><PERSON>|ah<PERSON><PERSON><PERSON>|cpratha<PERSON>|atandon|richard.lee|pmathew"}, "PhoneNumberUsers": {"UserNames": "richard.lee|jc<PERSON>|Cprathapan|pmathew"}, "Impersonation": {"Username": "Bury-PM", "Impersonate": "No"}, "DetailedErrors": true, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*"}