﻿@using Microsoft.AspNetCore.Components.Authorization
@using PracticeDirectory.Client.Service
@using PracticeDirectory.Client.Shared.Configuration
@using PracticeDirectory.Client.Shared.Extensions
@using PracticeDirectory.Client.Shared.Models
@using PracticeDirectory.Client.Shared.Models.Practices
@inject IHttpClientFactory ClientFactory
@inject PracticeContextService PracticeContext
@inject NavigationManager Navigation
@inject UserContextService UserContext
@inject IJSRuntime JSRuntime

<h3>Clinicians team for @Practice.WebsiteName</h3>
<style>
	/* Ensure all cards have the same height and width */
	.profile-card {
	min-width: 230px;
	max-width: 250px;
	height: 180px;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	}

	/* Ensure the content inside the card is spaced evenly */
	.profile-card .me-3 {
	flex: 1;
	overflow: hidden;
	}

	.profile-card .ms-auto {
	margin-top: auto;
	}

	.card {
	border: 1px solid #ddd;
	box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
	}

	.profile-card .fw-bold {
	font-size: 1.1rem;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	}

</style>

<CascadingValue Value="@Practice.Id" IsFixed="false">
	<div class="d-flex flex-wrap justify-content-start gap-4">
		@foreach (var profile in practicePeople)
		{
			<div class="card profile-card p-4 shadow-sm rounded-4" style="flex: 1 1 300px; min-width: 355px; max-width: 355px;">
				<div class="d-flex flex-column justify-content-between h-100">
					<div class="mb-2">
						<div class="d-flex justify-content-between align-items-start">
							<div class="pe-3 flex-grow-1">
								<div class="fw-semibold fs-6 text-break">@profile.DisplayNameAs</div>
								<div class="fw-medium text-muted small">@profile.PersonRole</div>
								<div class="fw-medium text-muted small">@profile.GDCNumber</div>
							</div>
							<div>
								@if (profile.Id == 0)
								{
									if (AddMode)
									{
										<div class="d-flex flex-column gap-1">
											<a href="javascript: void(0);" @onclick="() => SetIsAddTrue(profile)" class="btn btn-outline-primary btn-sm" style="min-width: 96px; max-width: 96px;">
												Add Profile
											</a>
										</div>
									}

								}
								else
								{
									<div class="d-flex flex-column gap-1">
										<a href="javascript: void(0);" @onclick="() => SetIsAddFalse(profile)" class="btn btn-outline-primary btn-sm" style="min-width: 96px; max-width: 96px;">
											View Profile
										</a>
										@if (isAdmin || AuthExtensions.UserIsPracticeManagerForPracticeId(Practice.Id, CurrentUserPracticeId))
										{
											<a href="practice/@Practice.Id/PublicProfileImage/@profile.PersonId" class="btn btn-outline-secondary btn-sm">Edit Image</a>
										}
									</div>
								}
							</div>
						</div>
					</div>

					<div>
						@{
							var resolvedStatus = statusList
							.FirstOrDefault(s => s.Id == profile.Status)?
							.StatusName ?? "Not Set";
						}
						<div class="d-flex align-items-center gap-2 border-start border-3 border-info ps-3 py-2 bg-light rounded shadow-sm text-secondary small">
							<i class="bi bi-check-circle text-info"></i>
							<span><strong>Status:</strong> @resolvedStatus</span>
						</div>
					</div>
				</div>
			</div>
		}
	</div>

</CascadingValue>

@if (showErrorMessage)
{
	<div class="alert alert-danger" role="alert">
		Public Profiles Not Found
	</div>
}

@code {
	[Parameter]
	public DirectoryPracticeModel Practice { get; set; } = new();
	[Parameter]
	public string PracticeName { get; set; } = string.Empty;
	private IEnumerable<PublicProfileModel> practicePeople = [];
	private bool showErrorMessage = false;

	[CascadingParameter]
	private Task<AuthenticationState> authenticationStateTask { get; set; } = default!;
	private string userName = string.Empty;
	private string isAuthenticated = string.Empty;
	private List<StatusRecord> statusList = new();
	private int CurrentUserPracticeId;
	private bool AddMode = false;
	private bool isAdmin = false;

	protected override async Task OnInitializedAsync()
	{
		var authState = await authenticationStateTask;
		userName = authState.GetWindowsUserName(UserContext.OverrideUsername);
		isAuthenticated = authState.GetWindowsUserIsAuthenticated().ToYesNo();
		isAdmin = AuthExtensions.IsUserAdmin(authState.GetOriginalWindowsUserName());

		if (string.IsNullOrWhiteSpace(UserContext.OverrideUsername))
		{
			var storedOverride = await JSRuntime.InvokeAsync<string>("localStorage.getItem", "overrideUsername");
			if (!string.IsNullOrWhiteSpace(storedOverride))
			{
				UserContext.OverrideUsername = storedOverride;
			}
		}

		CurrentUserPracticeId = authState.GetPracticeId(ClientFactory, UserContext.OverrideUsername);
	}

	private void SetIsAddTrue(PublicProfileModel profile)
	{
		PracticeContext.IsAdd = true;
		Navigation.NavigateTo($"publicprofile/{profile.PersonId}");

	}

	// This method will set IsAdd to false when viewing a profile
	private void SetIsAddFalse(PublicProfileModel profile)
	{
		PracticeContext.IsAdd = false;
		Navigation.NavigateTo($"publicprofile/{profile.PersonId}");
	}

	private async Task GetStatusesAsync()
	{
		var httpClient = ClientFactory.CreateClient(ApiSettings.Api);
		httpClient.DefaultRequestHeaders.Add("X-ORIG-NAME", userName);

		try
		{
			var response = await httpClient.GetFromJsonAsync<Root>("publicprofileimages/status");
			statusList = response?.records ?? new List<StatusRecord>();
		}
		catch (Exception)
		{
			// Handle error or log
			statusList = new List<StatusRecord>();
		}
	}
	protected override async Task OnParametersSetAsync()
	{
		if (string.IsNullOrWhiteSpace(UserContext.OverrideUsername))
		{
			var storedOverride = await JSRuntime.InvokeAsync<string>("localStorage.getItem", "overrideUsername");
			if (!string.IsNullOrWhiteSpace(storedOverride))
			{
				UserContext.OverrideUsername = storedOverride;
			}
		}

		var authState = await authenticationStateTask;
		userName = authState.GetWindowsUserName(UserContext.OverrideUsername);
		isAuthenticated = authState.GetWindowsUserIsAuthenticated().ToYesNo();

		await JSRuntime.InvokeVoidAsync("localStorage.setItem", "practiceId", Practice.Id);
		PracticeContext.PracticeId = Practice.Id;
		bool isCurrentManager = await GetCurrentPMAsync(Practice.Id, CurrentUserPracticeId);
		await GetPracticePeopleAsync();
		await GetStatusesAsync();

		if (isCurrentManager)
		{
			AddMode = true;
		}

		StateHasChanged();
	}

	private async Task<bool> GetCurrentPMAsync(int ParentPractice, int CurrentPractice)
	{
		if (ParentPractice != 0 && CurrentPractice != 0)
		{
			return ParentPractice == CurrentPractice;
		}

		await Task.CompletedTask;
		return false;
	}


	private async Task GetPracticePeopleAsync()
	{
		// Name of client is critical for Windows Auth
		var httpClient = ClientFactory.CreateClient(ApiSettings.Api);
		httpClient.DefaultRequestHeaders.Add("X-ORIG-NAME", userName);

		try
		{
			showErrorMessage = false;
			var response = await httpClient.GetFromJsonAsync<IEnumerable<PublicProfileModel>>($"publicprofile/practice/{Practice.Id}");

			practicePeople = response?.OrderBy(r => r.DisplayNameAs).ToList() ?? [];
		}
		catch (Exception)
		{
			showErrorMessage = true;
		}
	}
}
