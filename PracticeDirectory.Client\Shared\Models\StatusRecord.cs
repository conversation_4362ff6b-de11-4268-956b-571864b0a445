﻿namespace PracticeDirectory.Client.Shared.Models
{
    public class StatusRecord
    {
        public int Id { get; set; }
        public string StatusName { get; set; } = string.Empty;
        public bool IsActive { get; set; }
    }

    public class Metadata
    {
        public int totalCount { get; set; }
    }

    public class Root
    {
        public List<StatusRecord> records { get; set; } = [];
        public Metadata metadata { get; set; } = new();
    }

}
