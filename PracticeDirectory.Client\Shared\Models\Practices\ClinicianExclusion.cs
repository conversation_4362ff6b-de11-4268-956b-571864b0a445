﻿using System.ComponentModel.DataAnnotations;

namespace PracticeDirectory.Client.Shared.Models.Practices
{
    public class ClinicianExclusion
    {
        [Key]
        public int Id { get; set; }  

        [Required]
        public string PersonId { get; set; } = string.Empty;

        [Required]
        public string Reason { get; set; } = string.Empty;

        public DateTime ExclusionDate { get; set; } = DateTime.UtcNow;
        public DateTime ReviewDate { get; set; } = DateTime.UtcNow.AddDays(30);

        public int PracticeId { get; set; }  
        public DateTime CreatedDate { get; set; } = DateTime.UtcNow;
        public DateTime LastUpdateDate { get; set; } = DateTime.UtcNow;
        public string? CreatedBy { get; set; }
        public string? LastUpdateBy { get; set; }
    }
}
