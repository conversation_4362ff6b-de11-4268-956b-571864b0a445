﻿using Microsoft.AspNetCore.Components.Authorization;
using System.Security.Claims;

namespace PracticeDirectory.Client.Shared.Providers;

public class CustomAuthenticationStateProvider(IHttpContextAccessor httpContextAccessor) : AuthenticationStateProvider
{
	public override Task<AuthenticationState> GetAuthenticationStateAsync()
	{
		return Task.FromResult(new AuthenticationState(new ClaimsPrincipal(httpContextAccessor.HttpContext!.User.Identity!)));
	}
}
