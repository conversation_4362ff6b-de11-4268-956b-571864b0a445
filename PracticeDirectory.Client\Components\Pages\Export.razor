﻿@page "/export"
@using Microsoft.AspNetCore.Components.Web

<style>
    .instructions {
        background-color: #f8f9fa;
        padding: 1.5rem;
        border-left: 4px solid #0d6efd;
        border-radius: 0.5rem;
    }
</style>
<PageTitle>Export Practice Data</PageTitle>

<h2 class="mb-4">Practice Directory Exporter</h2>

<iframe title="Practice Directory Exporter"
        width="100%"
        height="541.25"
        src="https://app.powerbi.com/reportEmbed?reportId=925a6472-0e3f-471b-bcb9-f16f7e5d5ab7&appId=2861092c-33b9-41ef-9818-432fab42c713&autoAuth=true&ctid=29d4f8a4-6bc4-4032-b2c1-5ed82e97e377"
        frameborder="0"
        allowFullScreen="true"
        class="mb-4" 
        onload="console.log('Power BI iframe loaded')">
    
</iframe>

<div class="instructions">
    <h3>How to Export Data:</h3>
    <p>To export data from the table:</p>
    <ol>
        <li>Hover over the data table you want to export</li>
        <li>Click on the three dots (⋯) in the top-right corner of the visual</li>
        <li>Select <strong>"Export data"</strong> from the dropdown</li>
        <li>Choose your preferred format (CSV, Excel, etc.)</li>
    </ol>
</div>
