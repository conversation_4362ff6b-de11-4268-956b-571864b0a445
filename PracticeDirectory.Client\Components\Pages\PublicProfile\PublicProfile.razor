﻿@page "/publicprofile/{PublicPersonId}"
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.Extensions.Options
@using PracticeDirectory.Client.Service
@using PracticeDirectory.Client.Shared.Configuration
@using PracticeDirectory.Client.Shared.Extensions
@using PracticeDirectory.Client.Shared.Models
@using PracticeDirectory.Client.Shared.Models.Practices
@using System.Text.Json
@using System.Text
@using System.Net;
@inject IHttpClientFactory ClientFactory
@inject PracticeContextService PracticeContext
@inject NavigationManager Navigation
@inject UserContextService UserContext
@inject IJSRuntime JSRuntime
@inject IOptions<ApiSettings> Apiconfig;

@{
    var resolvedStatus = statusList
        .FirstOrDefault(s => s.Id == @PublicProfileDetail.Status)?
        .StatusName ?? "Not Set";

}

@if (showDetail)
{
    <!-- Profile Title and Status Side-by-Side -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h3 class="profile-title">Website Profile for Clinician @PublicProfileDetail.DisplayNameAs</h3>

        <!-- Status Box (on the right) -->
        <div class="alert alert-info p-2 m-0 rounded" role="alert" style="border: 2px solid #46aeab; background-color: #f0f9f9; max-width: 300px;">
            <strong>Status:</strong> @resolvedStatus
        </div>
    </div>

    <div class="profile-container">
        <div class="row mt-4 mb-4">
            <div class="col-md-2 fw-bold label">
                Name
            </div>
            <div class="col-md-10">
                @if (ViewMode)
                {
                    <p>@PublicProfileDetail.DisplayNameAs</p>
                }
                else
                {
                    @if (editMode)
                    {
                        <InputText @bind-Value="PublicProfileDetail.DisplayNameAs" class="input-field" />
                    }
                    else
                    {
                        <p>@PublicProfileDetail.DisplayNameAs</p>
                    }
                }
            </div>
        </div>

        <div class="row mt-4 mb-4">
            <div class="col-md-2 fw-bold label">
                GDC Number
            </div>
            <div class="col-md-10">
                <p>@PublicProfileDetail.GDCNumber</p>
            </div>
        </div>

        <div class="row mt-4 mb-4">
            <div class="col-md-2 fw-bold label">
                Qualifications
            </div>
            <div class="col-md-10">
                <p>@qualification</p>
            </div>
        </div>

        <div class="row mb-4">
            <div class="col-md-2 fw-bold label">
                Bio
            </div>
            <div class="col-md-10">
                @if (ViewMode)
                {
                    @if (!string.IsNullOrWhiteSpace(PublicProfileDetail.DraftBio))
                    {
                        <p>@PublicProfileDetail.DraftBio</p>
                    }
                    else
                    {
                        <p>@PublicProfileDetail.Bio</p>
                    }
                }
                else
                {
                    @if (editMode)
                    {
                        <InputTextArea @bind-Value="PublicProfileDetail.DraftBio" class="input-textarea" />
                    }
                    else
                    {
                        @if (!string.IsNullOrWhiteSpace(PublicProfileDetail.DraftBio))
                        {
                            <p>@PublicProfileDetail.DraftBio</p>
                        }
                        else
                        {
                            <p>@PublicProfileDetail.Bio</p>
                        }
                    }
                }
            </div>
        </div>


        @if (!string.IsNullOrWhiteSpace(exclusionEntry.Reason))
        {
            <div class="row mt-4 mb-4">
                <div class="col-md-2 fw-bold label">
                    Exclusion
                </div>
                <div class="col-md-10">
                    <p><strong>Reason:</strong> @exclusionEntry.Reason</p>
                    <p><strong>Exclusion Date:</strong> @exclusionEntry.ExclusionDate.ToShortDateString()</p>
                    <p><strong>Review Date:</strong> @exclusionEntry.ReviewDate.ToShortDateString()</p>
                    <p class="text-danger fw-bold">Status: Temporarily hidden from Website</p>
                </div>
            </div>
        }
    </div>
    <div class="btn-container d-flex justify-content-start gap-3 mb-4">
        @if (!ViewMode)
        {

            <HxButton Text="@((editMode ? "Discard Changes" : "Edit Profile"))" Color="ThemeColor.Primary" @onclick="ChangeEditMode" class="btn btn-primary" />
            @if (editMode)
            {
                <HxButton Text="Save"
                Color="ThemeColor.Primary"
                @onclick="SaveAllChanges"
                class="btn btn-primary" />
            }
            @if (@PublicProfileDetail.Status == 0)
            {
                <HxButton Text="Submit for Approval" Color="ThemeColor.Primary" @onclick="SubmitforApproval" class="btn btn-primary" />
            }
            <HxButton Text="Remove from Website" Color="ThemeColor.Secondary" @onclick="async () => await OpenModal(PublicProfileDetail.PersonId ?? string.Empty)" class="btn btn-secondary" />
        }
        @if (PublicProfileDetail.Status == 2 && approvedImages.Count() != 0)
        {
            <a class="btn btn-secondary" href="@(Apiconfig.Value.BackendUrl)publicprofilecarambapacks/@(PublicProfileDetail.PersonId)">Download Caramba Pack</a>
        }
        <HxButton Text="Back To Practice" Color="ThemeColor.Secondary" @onclick="BackToPractice" class="btn btn-secondary" />

    </div>
    @if (showModal)
    {
        <div class="modal fade show d-block" style="background-color: rgba(0, 0, 0, 0.5);" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Exclude from Website</h5>
                        <button type="button" class="btn-close" @onclick="CloseModal"></button>
                    </div>
                    <div class="modal-body">
                        <label>Reason</label>
                        <textarea class="form-control" @bind="exclusionEntry.Reason"></textarea>

                        <label class="mt-3">Exclusion Date</label>
                        <input type="date" class="form-control" @bind="exclusionEntry.ExclusionDate" />

                        <label class="mt-3">Review Date</label>
                        <input type="date" class="form-control" @bind="exclusionEntry.ReviewDate" />

                        @if (!string.IsNullOrEmpty(validationMessage))
                        {
                            <div class="alert alert-danger" role="alert">
                                @validationMessage
                            </div>
                        }
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" @onclick="CloseModal">Cancel</button>
                        <button class="btn btn-danger" @onclick="DeleteExclusion">Delete</button>
                        <button class="btn btn-primary" @onclick="SaveExclusion">Save</button>
                    </div>
                </div>
            </div>
        </div>
    }

    @if (!string.IsNullOrEmpty(errorMessage))
    {
        <div class="alert alert-danger" role="alert" style="max-width: 600px; margin: 0 auto;">
            @errorMessage
        </div>
    }
}
else
{
    <div class="mt-4">
        <div class="spinner-grow text-muted"></div>
        <div class="spinner-grow text-muted"></div>
        <div class="spinner-grow text-muted"></div>
    </div>
}

@code {
    [Parameter]
    public string PublicPersonId { get; set; } = string.Empty;
    private PublicProfileModel PublicProfileDetail = new();
    private PublicProfileQualificationsModel PublicProfileQualificationDetail = new();
    private bool editMode = false;
    private bool ViewMode = true;
    private string qualification = string.Empty;
    private int PracticeId;
    private int CurrentUserPracticeId;
    private string errorMessage = string.Empty;
    private List<StatusRecord> statusList = new();
    private bool showDetail = false;
    private bool showModal = false;
    private ClinicianExclusion exclusionEntry = new();
    private string validationMessage = string.Empty;
    private IEnumerable<PublicProfileImageModel> approvedImages = new PublicProfileImageModel[0];

    [CascadingParameter]
    private Task<AuthenticationState> authenticationStateTask { get; set; } = default!;

    private string userName = string.Empty;
    private string isAuthenticated = string.Empty;

    protected override async Task OnParametersSetAsync()
    {
        int practiceId = PracticeContext.PracticeId;
        if (practiceId == 0) { practiceId = await getPracticeIdFromLocalStorage(); }
        PracticeId = practiceId;
        int profileId = @PublicProfileDetail.Id;

        await GetStatusesAsync();

        if (PracticeId == 0)
        {
            PracticeId = PracticeContext.PracticeId;
            if (PracticeId == 0) { PracticeId = await getPracticeIdFromLocalStorage(); }
        }
        bool isCurrentManager = await GetCurrentPMAsync(PracticeId, CurrentUserPracticeId);

        if (isCurrentManager)
        {
            ViewMode = false;
        }



        if (!PracticeContext.IsAdd)
        {
            await LoadPublicProfile();
            await LoadExclusionIfExists(PublicProfileDetail.PersonId!);
        }
        else
        {
            await LoadHRPublicProfile();
        }

        approvedImages = await SearchPublicProfileImagesAsync();
    }

    private async Task GetStatusesAsync()
    {
        var httpClient = ClientFactory.CreateClient(ApiSettings.Api);
        httpClient.DefaultRequestHeaders.Add("X-ORIG-NAME", userName);

        try
        {
            var response = await httpClient.GetFromJsonAsync<Root>("publicprofileimages/status");
            statusList = response?.records ?? new List<StatusRecord>();
        }
        catch (Exception)
        {
            // Handle error or log
            statusList = new List<StatusRecord>();
        }
    }

    private async Task LoadExclusionIfExists(string personId)
    {
        var httpClient = ClientFactory.CreateClient(ApiSettings.Api);
        httpClient.DefaultRequestHeaders.Add("X-ORIG-NAME", userName);

        try
        {
            var response = await httpClient.GetAsync($"clinicianexclusion/{personId}");
            if (response.IsSuccessStatusCode)
            {
                var exclusion = await response.Content.ReadFromJsonAsync<ClinicianExclusion>();
                if (exclusion != null)
                {
                    exclusionEntry = exclusion;
                }
            }
        }
        catch
        {
            // Optionally handle/log error
        }
    }


    private void CloseModal()
    {
        showModal = false;
    }

    private async Task DeleteExclusion()
    {
        var httpClient = ClientFactory.CreateClient(ApiSettings.Api);
        httpClient.DefaultRequestHeaders.Add("X-ORIG-NAME", userName);

        var response = await httpClient.DeleteAsync($"clinicianexclusion/{exclusionEntry.PersonId}");
        response.EnsureSuccessStatusCode();
        if (response.IsSuccessStatusCode)
        {
            CloseModal();
        }
        else
        {
            validationMessage = "Failed to delete the exclusion.";
        }
    }



    protected override async Task OnInitializedAsync()
    {
        var authState = await authenticationStateTask;
        userName = authState.GetWindowsUserName(UserContext.OverrideUsername);
        userName = string.IsNullOrWhiteSpace(UserContext.OverrideUsername) ? userName : UserContext.OverrideUsername;
        isAuthenticated = authState.GetWindowsUserIsAuthenticated().ToYesNo();
        UserContext.OverrideUsername = UserContext.OverrideUsername;

        if (string.IsNullOrWhiteSpace(UserContext.OverrideUsername))
        {
            var storedOverride = await JSRuntime.InvokeAsync<string>("localStorage.getItem", "overrideUsername");
            if (!string.IsNullOrWhiteSpace(storedOverride))
            {
                UserContext.OverrideUsername = storedOverride;
            }
        }

        CurrentUserPracticeId = authState.GetPracticeId(ClientFactory, UserContext.OverrideUsername);
        PracticeId = PracticeContext.PracticeId;
        if (PracticeId == 0) { PracticeId = await getPracticeIdFromLocalStorage(); }
    }

    private async Task<bool> GetCurrentPMAsync(int ParentPractice, int CurrentPractice)
    {
        if (ParentPractice != 0 && CurrentPractice != 0)
        {
            return ParentPractice == CurrentPractice;
        }

        await Task.CompletedTask;
        return false;
    }


    private async Task LoadPublicProfile()
    {
        try
        {
            showDetail = false;
            var httpClient = ClientFactory.CreateClient(ApiSettings.Api);
            httpClient.DefaultRequestHeaders.Add("X-ORIG-NAME", userName);
            var response = await httpClient.GetFromJsonAsync<PublicProfileModel>($"publicprofile/{PublicPersonId}");
            PublicProfileDetail = response ?? new PublicProfileModel();
            qualification = await GetQualificationAsync(PublicProfileDetail.GDCNumber ?? "");
        }
        finally
        {
            showDetail = true;
        }
    }

    private async Task LoadHRPublicProfile()
    {
        try
        {
            showDetail = false;
            var httpClient = ClientFactory.CreateClient(ApiSettings.Api);
            httpClient.DefaultRequestHeaders.Add("X-ORIG-NAME", userName);
            var response = await httpClient.GetFromJsonAsync<PublicProfileModel>($"publicprofile/Hrpersonid/{PublicPersonId}");
            PublicProfileDetail = response ?? new PublicProfileModel();
            qualification = await GetQualificationAsync(PublicProfileDetail.GDCNumber ?? "");
        }
        finally
        {
            showDetail = true;
        }
    }


    private async Task<int> GetPracticeManagerAsync()
    {
        var httpClient = ClientFactory.CreateClient(ApiSettings.Api);
        httpClient.DefaultRequestHeaders.Add("X-ORIG-NAME", userName);

        var responseMessage = await httpClient.GetAsync($"/authorization/name?ntusername={userName}");
        int practiceId = 0; // Default practiceId is 0 if not found

        if (responseMessage.IsSuccessStatusCode)
        {
            var jsonString = await responseMessage.Content.ReadAsStringAsync();
            using var jsonDoc = JsonDocument.Parse(jsonString);

            if (jsonDoc.RootElement.TryGetProperty("practice_Id", out var practiceIdElement))
            {
                practiceId = practiceIdElement.GetInt32();
            }
        }
        else
        {
            Console.WriteLine($"Failed to fetch Practice Manager for user {userName}. Status: {responseMessage.StatusCode}");
        }

        return practiceId;
    }

    private async Task<string> GetQualificationAsync(string gdcNumber)
    {
        var httpClient = ClientFactory.CreateClient(GDCApiSettings.Api);

        string qualification = string.Empty;

        try
        {
            var responseMessage = await httpClient.GetAsync($"?gdcNumber={gdcNumber}");

            if (responseMessage.IsSuccessStatusCode)
            {
                var jsonString = await responseMessage.Content.ReadAsStringAsync();
                using var jsonDoc = JsonDocument.Parse(jsonString);

                if (jsonDoc.RootElement.TryGetProperty("qualifications", out var qualificationElement))
                {
                    qualification = qualificationElement.GetString() ?? "";
                }
            }
        }
        catch (Exception)
        {
            // Handle error here if needed
        }
        string decodedQualification = WebUtility.HtmlDecode(qualification);
        return decodedQualification;
    }

    private void ChangeEditMode()
    {
        editMode = !editMode;

        if (editMode && string.IsNullOrWhiteSpace(PublicProfileDetail.DraftBio))
        {
            PublicProfileDetail.DraftBio = PublicProfileDetail.Bio;
        }
    }

    private async Task SaveExclusion()
    {
        if (string.IsNullOrWhiteSpace(exclusionEntry.Reason))
        {
            // You can show a message to the user here (e.g., using a toast, alert, or a validation summary)
            validationMessage = "Please enter a reason.";
            return;
        }
        if (exclusionEntry.Id == 0)
        {
            var httpClient = ClientFactory.CreateClient(ApiSettings.Api);
            httpClient.DefaultRequestHeaders.Add("X-ORIG-NAME", userName);
            using HttpResponseMessage response = await httpClient.PostAsJsonAsync($"clinicianexclusion", exclusionEntry);
            response.EnsureSuccessStatusCode();
        }
        else
        {
            var httpClient = ClientFactory.CreateClient(ApiSettings.Api);
            httpClient.DefaultRequestHeaders.Add("X-ORIG-NAME", userName);
            using HttpResponseMessage response = await httpClient.PutAsJsonAsync($"clinicianexclusion/{exclusionEntry.Id}", exclusionEntry);
            response.EnsureSuccessStatusCode();
        }

        showModal = false;

        // Optionally refresh profile if needed
        await LoadPublicProfile();

    }

    private async Task SaveAllChanges()
    {
        var httpClient = ClientFactory.CreateClient(ApiSettings.Api);
        httpClient.DefaultRequestHeaders.Add("X-ORIG-NAME", userName);
        PublicProfileDetail.Status = 0;

        if (PublicProfileDetail.Id == 0)
        {
            PublicProfileQualificationDetail.Qualifications = qualification;
            PublicProfileQualificationDetail.PersonId = PublicPersonId;
            PublicProfileQualificationDetail.GDCNumber = PublicProfileDetail.GDCNumber ?? "";

            using HttpResponseMessage qalresponse = await httpClient.PostAsJsonAsync("publicprofilequalifications", PublicProfileQualificationDetail);
            qalresponse.EnsureSuccessStatusCode();

            if (!string.IsNullOrWhiteSpace(PublicProfileDetail.Bio))
            {
                PublicProfileDetail.DraftBio = PublicProfileDetail.Bio;
            }
            using HttpResponseMessage response = await httpClient.PostAsJsonAsync("publicprofile", PublicProfileDetail);
            response.EnsureSuccessStatusCode();
        }
        else
        {
            using HttpResponseMessage response = await httpClient.PutAsJsonAsync($"publicprofile/{PublicPersonId}", PublicProfileDetail);
            response.EnsureSuccessStatusCode();
        }
        await LoadPublicProfile();
        editMode = false;
    }
    private async Task OpenModal(string personId)
    {
        showModal = true; // show immediately for better responsiveness

        var httpClient = ClientFactory.CreateClient(ApiSettings.Api);
        httpClient.DefaultRequestHeaders.Add("X-ORIG-NAME", userName);

        try
        {
            var response = await httpClient.GetAsync($"clinicianexclusion/{personId}");
            if (response.IsSuccessStatusCode)
            {
                var exclusion = await response.Content.ReadFromJsonAsync<ClinicianExclusion>();
                if (exclusion?.Id == 0)
                {
                    exclusion = GetDefaultExclusion(personId);
                }
                exclusionEntry = exclusion ?? GetDefaultExclusion(personId);
            }
            else if (response.StatusCode == HttpStatusCode.NotFound)
            {
                exclusionEntry = GetDefaultExclusion(personId);
            }
            else
            {
                validationMessage = "Could not retrieve exclusion details.";
                exclusionEntry = GetDefaultExclusion(personId);
            }
        }
        catch
        {
            validationMessage = "Error retrieving exclusion data.";
            exclusionEntry = GetDefaultExclusion(personId);
        }

        StateHasChanged();
    }



    private ClinicianExclusion GetDefaultExclusion(string personId)
    {
        return new ClinicianExclusion
            {
                PersonId = personId,
                ExclusionDate = DateTime.Today,
                ReviewDate = DateTime.Today.AddDays(30),
                Reason = string.Empty,
                PracticeId = PracticeId
            };
    }

    private int CountWords(string text)
    {
        if (string.IsNullOrWhiteSpace(text)) return 0;
        return text.Split(new[] { ' ', '\n', '\r', '\t' }, StringSplitOptions.RemoveEmptyEntries).Length;
    }



    private async Task SubmitforApproval()
    {
        var wordCount = CountWords(PublicProfileDetail.DraftBio ?? "");
        if (wordCount > 200)
        {
            errorMessage = "Bio exceeds the 200-word limit. Please shorten it before submitting.";
        }
        else if (wordCount < 150)
        {
            errorMessage = "Bio should be minimum of 150 words";
        }
        else
        {
            errorMessage = string.Empty;
            // Save DisplayNameAs, Bio, and Qualification in a single save operation
            var httpClient = ClientFactory.CreateClient(ApiSettings.Api);
            httpClient.DefaultRequestHeaders.Add("X-ORIG-NAME", userName);
            PublicProfileDetail.Status = 1;
            PublicProfileDetail.Practice = PracticeContext.PracticeId;
            if (PracticeId == 0) { PracticeId = await getPracticeIdFromLocalStorage(); }

            using HttpResponseMessage response = await httpClient.PutAsJsonAsync($"publicprofile/{PublicPersonId}", PublicProfileDetail);

            // Ensure the save operation is successful
            response.EnsureSuccessStatusCode();
            await LoadPublicProfile();
        }


    }
    private void BackToPractice()
    {
        Navigation.NavigateTo($"practice/{PracticeId}");
    }

    private async Task<int> getPracticeIdFromLocalStorage()
    {
        string storedPracticeId = await JSRuntime.InvokeAsync<string>("localStorage.getItem", "practiceId");
        if (!int.TryParse(storedPracticeId, out int practiceId))
            return 0;
        return practiceId;
    }

    private async Task<IEnumerable<PublicProfileImageModel>> SearchPublicProfileImagesAsync()
    {
        var httpClient = ClientFactory.CreateClient(ApiSettings.Api);
        httpClient.DefaultRequestHeaders.Add("X-ORIG-NAME", userName);

        var response = await httpClient.PostAsJsonAsync("publicprofileimages/search", new PublicProfileImageSearchOption { personId = PublicPersonId, approvalStatus = ["Approved"] });
        response.EnsureSuccessStatusCode();

        var content = await response.Content.ReadFromJsonAsync<PublicProfileImageSearchResponse>() ?? new();

        return content.Records;
    }
}