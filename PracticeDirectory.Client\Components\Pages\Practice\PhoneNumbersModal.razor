﻿@using PracticeDirectory.Client.Shared.Models.Practices
@using Microsoft.AspNetCore.Components.Authorization
@using PracticeDirectory.Client.Shared.Configuration
@using PracticeDirectory.Client.Shared.Extensions
@using PracticeDirectory.Client.Service
@inject IHttpClientFactory ClientFactory
@inject UserContextService UserContext
@inherits HxDialogBase<IEnumerable<PracticePhoneNumberModel>>

@code {
	[Parameter]
	public IEnumerable<PracticePhoneNumberModel> PhoneNumbers { get; set; } = [];

	[Parameter]
	public int PracticeId { get; set; }

	[CascadingParameter]
	private Task<AuthenticationState> authenticationStateTask { get; set; } = default!;

	private string userName = string.Empty;
	private string isAuthenticated = string.Empty;
	private IEnumerable<ContactTypeModel> contactTypes = [];

	protected override async Task OnParametersSetAsync()
	{
		var authState = await authenticationStateTask;
		userName = authState.GetWindowsUserName(UserContext.OverrideUsername);
		isAuthenticated = authState.GetWindowsUserIsAuthenticated().ToYesNo();

		await GetContactTypes();
	}

	private async Task GetContactTypes()
	{
		var httpClient = ClientFactory.CreateClient(ApiSettings.Api);
		httpClient.DefaultRequestHeaders.Add("X-ORIG-NAME", userName);

		var response = await httpClient.GetFromJsonAsync<IEnumerable<ContactTypeModel>>($"practicephonenumbers/contact-types");
		contactTypes = response ?? [];
	}

	private IEnumerable<ContactTypeLookupItemModel>? GetMechanismTypes() => contactTypes.FirstOrDefault(c => c.MechanismName == "Type")?.LookupItems;
	private IEnumerable<ContactTypeLookupItemModel>? GetPurposeTypes() => contactTypes.FirstOrDefault(c => c.MechanismName == "Purpose")?.LookupItems;
	private IEnumerable<ContactTypeLookupItemModel>? GetUsageTypes() => contactTypes.FirstOrDefault(c => c.MechanismName == "Usage")?.LookupItems;

	private void NewNumber()
	{
		PhoneNumbers = PhoneNumbers.Append(new PracticePhoneNumberModel
		{
				PracticeId = PracticeId,
				ContactMechanism = "LANDLINE",
				Usage = "Business",
				Purpose = "General Enquiries",
				PhoneNumber = "0000"
		});
	}

	private async Task DeletePhoneNumber(PracticePhoneNumberModel contact)
	{
		var httpClient = ClientFactory.CreateClient(ApiSettings.Api);
		httpClient.DefaultRequestHeaders.Add("X-ORIG-NAME", userName);

		var response = await httpClient.DeleteAsync($"practicephonenumbers/{contact.Id}");
		response.EnsureSuccessStatusCode();

		PhoneNumbers = PhoneNumbers.Where(p => p.Id != contact.Id);
	}
}

<HxModal Title="Edit Contacts" @ref="modal" Size="ModalSize.Large">
	<BodyTemplate>
		<table class="table">
			<tr>
				<th class="text-center">Phone</th>
				<th class="text-center">Type</th>
				<th class="text-center">Usage</th>
				<th class="text-center">Purpose</th>
				<th class="text-center">Website?</th>
				<th class="text-center"></th>
			</tr>
			@foreach (var contact in PhoneNumbers)
			{
				<tr>
					<td class="text-center"><HxInputText @bind-Value="contact.PhoneNumber"></HxInputText></td>
					<td class="text-center">
						<HxSelect TItem="ContactTypeLookupItemModel"
								  TValue="string"
								  Data="GetMechanismTypes()"
								  @bind-Value="contact.ContactMechanism"
								  TextSelector="@(c => c.Code)"
								  ValueSelector="@(c => c.Code)"
								  Enabled="false" />
					</td>
					<td class="text-center">
						<HxSelect TItem="ContactTypeLookupItemModel"
								  TValue="string"
								  Data="GetUsageTypes()"
								  @bind-Value="contact.Usage"
								  TextSelector="@(c => c.Code)"
								  ValueSelector="@(c => c.Code)"
								  Enabled="false" />
					</td>
					<td class="text-center">
						<HxSelect TItem="ContactTypeLookupItemModel"
								  TValue="string"
								  Data="GetPurposeTypes()"
								  @bind-Value="contact.Purpose"
								  TextSelector="@(c => c.Code)"
								  ValueSelector="@(c => c.Code)"
								  Enabled="false" />
					</td>
					<td class="text-center">
						<HxCheckbox @bind-Value="contact.UseForWebsite" />
					</td>
					<td>
						<HxButton Text="Delete" OnClick="async () => await DeletePhoneNumber(contact)" />
					</td>
				</tr>
			}
			<div>
				<HxButton Text="Add New..." Color="ThemeColor.Secondary" OnClick="() => NewNumber()" />
			</div>
		</table>
	</BodyTemplate>
	<FooterTemplate>
		<HxButton Text="Save" Color="ThemeColor.Primary" OnClick="async () => await HideAsync(PhoneNumbers)" />
		<HxButton Text="Close" Color="ThemeColor.Secondary" OnClick="async () => await HideAsCancelledAsync()" />
	</FooterTemplate>
</HxModal>
