﻿namespace PracticeDirectory.Client.Shared.Configuration
{
    public class CustomAttributesSettings
    {
        public string CanEditeAttributes { get; set; } = string.Empty;
        public AttributeGroupSettings AttributeGroupSettings { get; set; } = new();

        public string[] GetEditableUserList() =>
            CanEditeAttributes?.Split('|', StringSplitOptions.RemoveEmptyEntries) ?? Array.Empty<string>();
    }

    public class AttributeGroupSettings : Dictionary<string, List<string>>
    {
    }
}
