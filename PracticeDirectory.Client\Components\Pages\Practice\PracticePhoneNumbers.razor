﻿@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.Extensions.Options
@using PracticeDirectory.Client.Shared.Configuration
@using PracticeDirectory.Client.Shared.Extensions
@using PracticeDirectory.Client.Shared.Models
@using PracticeDirectory.Client.Service
@using PracticeDirectory.Client.Shared.Models.Practices
@inject IHttpClientFactory ClientFactory
@inject UserContextService UserContext
@inject PracticeContextService PracticeContext
@inject IJSRuntime JSRuntime
@inject NavigationManager Navigation
@inject IOptions<PhoneNumberUsersSettings> PhoneNumberUsersConfig

@foreach (var contact in phoneNumbers.Records)
{
	<div class="row">
		<div class="col-md-3">
			<strong>Phone: </strong>
		</div>
		<div class="col-md-3">
			@contact.PhoneNumber
		</div>
	</div>
}

<HxButton Text="Edit" Color="ThemeColor.Primary" OnClick="HandleEditClick" Enabled="isPhoneNumberUser"></HxButton>
<PhoneNumbersModal @ref="modal" PhoneNumbers="phoneNumbers.Records" PracticeId="PracticeId"></PhoneNumbersModal>

@if (showErrorMessage || loadError)
{
	<div class="alert alert-danger" role="alert">
		@lastErrorMessage
	</div>
}

@code {
	[Parameter]
	public int PracticeId { get; set; }

	[CascadingParameter]
	private Task<AuthenticationState> authenticationStateTask { get; set; } = default!;

	private string userName = string.Empty;
	private string isAuthenticated = string.Empty;
	private PracticePhoneNumberFullModel phoneNumbers = new();
	private IEnumerable<ContactTypeModel> contactTypes = [];
	private bool loadError = false;
	PhoneNumbersModal modal = new();
	private bool showErrorMessage = false;
	private string lastErrorMessage = string.Empty;
	private bool isPhoneNumberUser = false;

	protected override async Task OnParametersSetAsync()
	{
		await LoadOverrideUsernameAsync();

		var authState = await authenticationStateTask;
		userName = authState.GetWindowsUserName(UserContext.OverrideUsername);
		isAuthenticated = authState.GetWindowsUserIsAuthenticated().ToYesNo();
		isPhoneNumberUser = IsPhoneNumberUser(userName);

		await JSRuntime.InvokeVoidAsync("localStorage.setItem", "practiceId", PracticeId);
		await GetPhoneNumbersAsync();
	}

	private async Task LoadOverrideUsernameAsync()
	{
		if (string.IsNullOrWhiteSpace(UserContext.OverrideUsername))
		{
			var storedOverride = await JSRuntime.InvokeAsync<string>("localStorage.getItem", "overrideUsername");
			if (!string.IsNullOrWhiteSpace(storedOverride))
			{
				UserContext.OverrideUsername = storedOverride;
			}
		}
	}

	private async Task GetPhoneNumbersAsync()
	{
		try
		{
			var httpClient = ClientFactory.CreateClient(ApiSettings.Api);
			httpClient.DefaultRequestHeaders.Add("X-ORIG-NAME", userName);

			var response = await httpClient.GetFromJsonAsync<PracticePhoneNumberFullModel>($"practicephonenumbers/{PracticeId}");
			phoneNumbers = response ?? new();
		}
		catch
		{
			loadError = true;
			lastErrorMessage = "Could not load content";
		}
	}

	private async Task HandleEditClick()
	{
		showErrorMessage = false;
		lastErrorMessage = string.Empty;
		var dialogResult = await modal.ShowAsync();

		if (dialogResult.Successful)
		{
			var phoneNumbers = dialogResult.Value;

			foreach (var number in phoneNumbers)
			{
				var httpClient = ClientFactory.CreateClient(ApiSettings.Api);
				httpClient.DefaultRequestHeaders.Add("X-ORIG-NAME", userName);

				var response = await httpClient.PostAsJsonAsync($"practicephonenumbers", number);

				try
				{
					response.EnsureSuccessStatusCode();
				}
				catch (Exception)
				{
					ShowErrorMessage();
				}
			}
		}

		await GetPhoneNumbersAsync();
	}

	private void ShowErrorMessage()
	{
		lastErrorMessage = "Changes were not saved";
		showErrorMessage = true;
	}

	private bool IsPhoneNumberUser(string user)
	{
		return PhoneNumberUsersConfig.Value
			.GetUserList()
			.Contains(user.Split('\\').Last(), StringComparer.OrdinalIgnoreCase);
	}
}