﻿namespace PracticeDirectory.Client.Shared.Models.Practices
{
    public class PracticeRegionPerson
    {
        public int PracticeId { get; set; }
        public string RegionName { get; set; } = string.Empty;
        public string RoleName { get; set; } = string.Empty;
        public string PersonName { get; set; } = string.Empty;

        public string? EmailAddress { get; set; } = string.Empty;
        public string? PhoneNumber { get; set; } = string.Empty;
    }
}
