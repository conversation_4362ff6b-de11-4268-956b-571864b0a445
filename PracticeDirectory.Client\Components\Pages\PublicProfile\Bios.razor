﻿@page "/bios"
@using Microsoft.AspNetCore.Components.Authorization
@using PracticeDirectory.Client.Shared.Configuration
@using PracticeDirectory.Client.Shared.Extensions
@using PracticeDirectory.Client.Shared.Models
@using PracticeDirectory.Client.Service
@inject IHttpClientFactory ClientFactory
@inject UserContextService UserContext

<PageTitle>Public Profile Bio's'</PageTitle>

<h1>@result.DisplayNameAs</h1>

<div class="container-fluid">
	<div class="row">
		<div class="col-md-2">
			Bio
		</div>
		<div class="col-md-10">
			@result.Bio
		</div>
	</div>
	<div class="row">
		<div class="col-md-2">
			GDC Number
		</div>
		<div class="col-md-10">
			@result.GDCNumber
		</div>
	</div>
</div>

@code {
	string baseUrl = string.Empty;
	private PublicProfileModel result = new();

	[CascadingParameter]
	private Task<AuthenticationState> authenticationStateTask { get; set; } = default!;
	private string userName = string.Empty;
	private string isAuthenticated = string.Empty;

	protected override async Task OnInitializedAsync()
	{
		var authState = await authenticationStateTask;
		userName = authState.GetWindowsUserName(UserContext.OverrideUsername);
		isAuthenticated = authState.GetWindowsUserIsAuthenticated().ToYesNo();

		await GetPublicProfilesAsync();
	}

	private async Task GetPublicProfilesAsync()
	{
		// Name of client is critical for Windows Auth
		var httpClient = ClientFactory.CreateClient(ApiSettings.Api);
		httpClient.DefaultRequestHeaders.Add("X-ORIG-NAME", userName);
		var response = await httpClient.GetFromJsonAsync<PublicProfileModel>($"publicprofile/1");

		result = response ?? new();
	}
}
