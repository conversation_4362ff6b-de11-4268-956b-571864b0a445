﻿namespace PracticeDirectory.Client.Shared.Models
{
    public class PublicProfileQualificationsModel
    {
        public int Id { get; set; }
        public string? PersonId { get; set; }
        public string GDCNumber { get; set; } = string.Empty;
        public string Qualifications { get; set; } = string.Empty;
        public DateTime? CreatedDate { get; set; }
        public DateTime? LastUpdateDate { get; set; }
        public string? LastUpdateBy { get; set; }
        public string? CreatedBy { get; set; }
    }
}
