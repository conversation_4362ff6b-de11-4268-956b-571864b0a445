﻿<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="Practice Directory" />
    <base href="@BaseUrl" />
    @((MarkupString)HxSetup.RenderBootstrapCssReference())
    <link rel="stylesheet" href="app.css" />
    <link rel="stylesheet" href="PracticeDirectory.Client.styles.css" />
    <link rel="icon" type="image/png" href="favicon.ico" />
    <HeadOutlet @rendermode="@(new InteractiveServerRenderMode(prerender: false))" />
</head>

<body>
    <Routes @rendermode="@(new InteractiveServerRenderMode(prerender: false))" />
    <script src="_framework/blazor.web.js"></script>
    @((MarkupString)@HxSetup.RenderBootstrapJavaScriptReference())
    <script src="js/imageDownloader.js"></script>
</body>

</html>

@code {
    [CascadingParameter] private HttpContext HttpContext { get; set; } = default!;
    private string BaseUrl => $"{HttpContext.Request.PathBase}/";
}