﻿@page "/practice/{PracticeId:int}"
@using Microsoft.AspNetCore.Components.Authorization
@using PracticeDirectory.Client.Components.Pages.ContractedHours
@using PracticeDirectory.Client.Shared.Configuration
@using PracticeDirectory.Client.Shared.Extensions
@using PracticeDirectory.Client.Shared.Models.Practices
@using PracticeDirectory.Client.Service
@inject IHttpClientFactory ClientFactory
@inject UserContextService UserContext

@if (showDetail)
{
	<h3 class="mb-4" style="color:#46aeab; font-size: 2rem; font-weight: 700; letter-spacing: 0.5px; line-height: 1.4;">@practice.Id - @practice.WebsiteName</h3>

	@if (practice.Id > 0)
	{
		<div class="row mt-4 mb-4">
			<div class="col-md-12">
				<PracticeAboutUs Practice="@practice"></PracticeAboutUs>
			</div>
		</div>
	}

	<div class="row mt-4 mb-4">
		<div class="col-md-4 mb-3">
			<div class="p-3 border rounded shadow-sm h-100">
				<h5 class="mb-2">Address</h5>
				<PracticeAddress Address="@practice.PostalAddress"></PracticeAddress>
			</div>
		</div>

		<div class="col-md-4 mb-3">
			<div class="p-3 border rounded shadow-sm h-100">
				<h5 class="mb-2">Location</h5>
				<div class="mb-2">
					<strong>Area:</strong> @practice.AreaName
				</div>
				<div>
					<strong>Region:</strong> @practice.RegionName
				</div>
			</div>
		</div>

		<div class="col-md-4 mb-3">
			<div class="p-3 border rounded shadow-sm h-100">
				<h5 class="mb-2">Contact</h5>
				<PracticePhoneNumbers PracticeId="@practice.Id"></PracticePhoneNumbers>
			</div>
		</div>
	</div>

	@if (practice.Id > 0)
	{
		<div class="row mt-4">
			<div class="col-md-12">
				<PracticeAdminTeam Practice="@practice"></PracticeAdminTeam>
			</div>
		</div>
	}

	@if (practice.Id > 0)
	{
		<div class="row mt-4 mb-4">
			<div class="col-md-12">
				<PracticeOpeningHours Practice="@practice"></PracticeOpeningHours>
			</div>
		</div>
		<div class="row mt-4 mb-4">
			<div class="col-md-12">
				<PracticeOpeningHours Practice="@practice"></PracticeOpeningHours>
			</div>
		</div>	
		
	}

	@if (practice.Id > 0)
	{
		<div class="row mt-4">
			<div class="col-md-12">
				<PracticeInvoices Practice="@practice"></PracticeInvoices>
			</div>
		</div>
	}
	@if (practice.Id > 0)
	{
		<div class="row mt-4">
			<div class="col-md-12">
				<PracticePeople Practice="@practice"></PracticePeople>
			</div>
		</div>
	}
}
else
{
	<div class="mt-4">
		<div class="spinner-grow text-muted"></div>
		<div class="spinner-grow text-muted"></div>
		<div class="spinner-grow text-muted"></div>
	</div>
}

@code {
	[Parameter]
	public int PracticeId { get; set; }
	private DirectoryPracticeModel practice = new();

	[CascadingParameter]
	private Task<AuthenticationState> authenticationStateTask { get; set; } = default!;
	private string userName = string.Empty;
	private string isAuthenticated = string.Empty;
	private bool showDetail = false;

	protected override async Task OnInitializedAsync()
	{
		var authState = await authenticationStateTask;
		userName = authState.GetWindowsUserName(UserContext.OverrideUsername);
		isAuthenticated = authState.GetWindowsUserIsAuthenticated().ToYesNo();
		userName = string.IsNullOrWhiteSpace(UserContext.OverrideUsername) ? userName : UserContext.OverrideUsername;
		UserContext.OverrideUsername = UserContext.OverrideUsername;
		await GetPracticeAsync();
	}

	private async Task GetPracticeAsync()
	{
		try
		{
			showDetail = false;
			// Name of client is critical for Windows Auth
			var httpClient = ClientFactory.CreateClient(ApiSettings.Api);
			httpClient.DefaultRequestHeaders.Add("X-ORIG-NAME", userName);
			var response = await httpClient.GetFromJsonAsync<DirectoryPracticeModel>($"directorypractice/{PracticeId}");

			practice = response ?? new();
		}
		finally
		{
			showDetail = true;
		}
	}
}
