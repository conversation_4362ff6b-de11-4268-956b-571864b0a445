using Microsoft.Extensions.Options;
using PracticeDirectory.Client.Shared.Configuration;
using System.Net.Http.Headers;
using System.Text.Json;
using System.Text.RegularExpressions;

namespace PracticeDirectory.Client.Service
{
    // Response model for FileStorageHub API
    public class FileStorageHubResponse
    {
        // Using JsonPropertyName to ensure correct mapping
        [System.Text.Json.Serialization.JsonPropertyName("fileName")]
        public string FileName { get; set; } = string.Empty;

        [System.Text.Json.Serialization.JsonPropertyName("fileExtension")]
        public string FileExtension { get; set; } = string.Empty;

        [System.Text.Json.Serialization.JsonPropertyName("fileBytes")]
        public string FileBytes { get; set; } = string.Empty;
    }

    public class FileStorageHubService : IFileStorageHubService
    {
        private readonly FileStorageHubSettings _fileStorageHubSettings;
        private readonly HttpClient _httpClient;

        // Shared JsonSerializerOptions instance
        private static readonly JsonSerializerOptions _jsonOptions = new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        };

        // Image cache to store downloaded images
        private static readonly Dictionary<string, ImageCacheItem> _imageCache = new();

        // Cache expiration time in minutes
        private const int CacheExpirationMinutes = 30;

        public FileStorageHubService(IOptions<FileStorageHubSettings> fileStorageHubSettings, HttpClient httpClient)
        {
            _fileStorageHubSettings = fileStorageHubSettings.Value;
            _httpClient = httpClient;

            _httpClient.DefaultRequestHeaders.Add("Accept", "*/*");
            _httpClient.DefaultRequestHeaders.Add("APIKey", _fileStorageHubSettings.APIKey);
        }

        public async Task<string> UploadImageAsync(string base64Data, string personalNumber, string fullName)
        {
            try
            {
                // Extract the file extension from the base64 data
                var match = Regex.Match(base64Data, @"data:image/([a-zA-Z0-9]+);base64,");
                string extension = match.Success ? $".{match.Groups[1].Value}" : ".jpg";

                // Remove the data:image/xxx;base64, prefix
                string cleanBase64 = base64Data;
                if (match.Success)
                {
                    cleanBase64 = base64Data.Substring(match.Value.Length);
                }
                else if (base64Data.Contains(","))
                {
                    // Try another approach to extract the base64 data
                    cleanBase64 = base64Data.Split(',')[1];
                }

                // Extract the original file name from the base64 data if possible
                string originalFileName = "image";
                var originalFileNameMatch = Regex.Match(base64Data, @"name=([^;]+);");
                if (originalFileNameMatch.Success)
                {
                    originalFileName = originalFileNameMatch.Groups[1].Value;
                    // Remove any invalid characters from the file name
                    originalFileName = Regex.Replace(originalFileName, @"[^\w\-\.]", "_");
                }

                // Generate a unique file name using the original file name, date with hour and minute, and a random component
                string uniqueFileName = $"{Path.GetFileNameWithoutExtension(originalFileName)}-{DateTime.Now:yyyy-MM-dd-HH-mm}-{Guid.NewGuid().ToString()[..8]}";

                // Generate the image path
                string imagePath = $"Profiles/{personalNumber}/{fullName}-{uniqueFileName}{extension}";

                // Convert base64 to bytes
                byte[] fileBytes;
                try
                {
                    fileBytes = Convert.FromBase64String(cleanBase64);
                }
                catch (FormatException)
                {
                    // If conversion fails, try with the original string
                    Console.WriteLine("Failed to convert cleaned base64, trying with original string");
                    fileBytes = Convert.FromBase64String(base64Data);
                }

                using (var fileStream = new MemoryStream(fileBytes))
                {
                    var content = new MultipartFormDataContent();
                    var streamContent = new StreamContent(fileStream);
                    streamContent.Headers.ContentType = new MediaTypeHeaderValue("application/octet-stream");

                    content.Add(streamContent, "file", imagePath);

                    string uploadUrl = _fileStorageHubSettings.URL + _fileStorageHubSettings.ImageUploadEndpointPath;
                    Console.WriteLine($"Uploading image to: {uploadUrl}");
                    Console.WriteLine($"Image path: {imagePath}");

                    HttpResponseMessage response = await _httpClient.PostAsync(uploadUrl, content);

                    // Log the response
                    string responseContent = await response.Content.ReadAsStringAsync();
                    Console.WriteLine($"Upload response: {response.StatusCode}");

                    response.EnsureSuccessStatusCode();

                    // Parse the response to get the file name
                    try
                    {
                        // Log the response content for debugging
                        Console.WriteLine($"Response content: {responseContent}");

                        // The response contains the file information including the file name
                        var responseObj = JsonSerializer.Deserialize<FileStorageHubResponse>(responseContent, _jsonOptions);

                        if (responseObj != null && !string.IsNullOrEmpty(responseObj.FileName))
                        {
                            Console.WriteLine($"Uploaded file name: {responseObj.FileName}");
                            // Return the file name that can be used for downloading
                            return responseObj.FileName;
                        }
                        else
                        {
                            Console.WriteLine("Response object is null or fileName is empty");

                            // Try to extract the file name from the response using a different approach
                            // For example, using regular expressions
                            var fileNameMatch = Regex.Match(responseContent, @"""fileName""\s*:\s*""([^""]+)""");
                            if (fileNameMatch.Success)
                            {
                                string fileName = fileNameMatch.Groups[1].Value;
                                Console.WriteLine($"Extracted file name using regex: {fileName}");
                                return fileName;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Error parsing response: {ex.Message}");
                        Console.WriteLine($"Stack trace: {ex.StackTrace}");
                    }

                    // If we couldn't parse the response, return the original path
                    return imagePath;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error uploading image: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                throw;
            }
        }

        public async Task<byte[]> DownloadImageAsync(string imagePath)
        {
            try
            {
                // If the imagePath is already a full URL, extract just the path part
                if (imagePath.StartsWith("http"))
                {
                    // Extract the path after the domain and API endpoint
                    var uri = new Uri(imagePath);
                    string path = uri.PathAndQuery;

                    // If the path contains the download endpoint, extract the image path
                    if (path.Contains("/Image/download/"))
                    {
                        imagePath = path[(path.IndexOf("/Image/download/") + "/Image/download/".Length)..];
                    }
                }

                string downloadUrl = $"{_fileStorageHubSettings.URL}{_fileStorageHubSettings.ImageDownloadEndpointPath}/{imagePath}";
                Console.WriteLine($"Downloading image from: {downloadUrl}");

                HttpResponseMessage response = await _httpClient.GetAsync(downloadUrl);

                // Log the response
                Console.WriteLine($"Download response: {response.StatusCode}");

                response.EnsureSuccessStatusCode();

                // Get the response content as string
                string responseContent = await response.Content.ReadAsStringAsync();

                try
                {
                    // Log the response content for debugging
                    Console.WriteLine($"Download response content: {responseContent[..Math.Min(200, responseContent.Length)]}...");

                    // Try to parse the response as a FileStorageHubResponse
                    var responseObj = JsonSerializer.Deserialize<FileStorageHubResponse>(responseContent, _jsonOptions);

                    if (responseObj != null && !string.IsNullOrEmpty(responseObj.FileBytes))
                    {
                        Console.WriteLine("Successfully parsed FileStorageHubResponse");
                        // Convert the base64 string to bytes
                        return Convert.FromBase64String(responseObj.FileBytes);
                    }
                    else
                    {
                        Console.WriteLine("Response object is null or fileBytes is empty");

                        // Try to extract the file bytes from the response using a different approach
                        // For example, using regular expressions
                        var fileBytesMatch = Regex.Match(responseContent, @"""fileBytes""\s*:\s*""([^""]+)""");
                        if (fileBytesMatch.Success)
                        {
                            string fileBytes = fileBytesMatch.Groups[1].Value;
                            Console.WriteLine($"Extracted file bytes using regex (first 50 chars): {fileBytes[..Math.Min(50, fileBytes.Length)]}...");
                            return Convert.FromBase64String(fileBytes);
                        }
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error parsing response as FileStorageHubResponse: {ex.Message}");
                    Console.WriteLine($"Stack trace: {ex.StackTrace}");
                    // If parsing fails, try to use the response content directly
                }

                // If we couldn't parse the response or it didn't contain FileBytes, return the raw content
                return await response.Content.ReadAsByteArrayAsync();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error downloading image: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                throw;
            }
        }

        public async Task<Dictionary<string, string>> DownloadImagesAsync(List<string> imagePaths)
        {
            var result = new Dictionary<string, string>();

            // Process images in parallel for better performance
            var tasks = imagePaths.Select(async imagePath =>
            {
                try
                {
                    string dataUrl = await GetImageDataUrlAsync(imagePath);
                    return (imagePath, dataUrl);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error downloading image {imagePath}: {ex.Message}");
                    return (imagePath, string.Empty);
                }
            });

            var results = await Task.WhenAll(tasks);

            foreach (var (imagePath, dataUrl) in results)
            {
                if (!string.IsNullOrEmpty(dataUrl))
                {
                    result[imagePath] = dataUrl;
                }
            }

            return result;
        }

        public async Task<string> GetImageDataUrlAsync(string imagePath)
        {
            // Check if the image is already in the cache
            if (_imageCache.TryGetValue(imagePath, out var cacheItem))
            {
                // Check if the cache item is still valid
                if ((DateTime.Now - cacheItem.CacheTime).TotalMinutes < CacheExpirationMinutes)
                {
                    Console.WriteLine($"Using cached image for {imagePath}");
                    return cacheItem.DataUrl;
                }

                // Remove expired cache item
                _imageCache.Remove(imagePath);
            }

            try
            {
                // Download the image
                byte[] imageBytes = await DownloadImageAsync(imagePath);

                // Try to parse the response as a FileStorageHubResponse
                string responseJson = System.Text.Encoding.UTF8.GetString(imageBytes);

                try
                {
                    var responseObj = JsonSerializer.Deserialize<FileStorageHubResponse>(responseJson, _jsonOptions);

                    if (responseObj != null && !string.IsNullOrEmpty(responseObj.FileBytes))
                    {
                        // Determine the MIME type based on the file extension
                        string mimeType = responseObj.FileExtension.ToLower() switch
                        {
                            ".jpg" => "image/jpeg",
                            ".jpeg" => "image/jpeg",
                            ".png" => "image/png",
                            ".gif" => "image/gif",
                            _ => "image/jpeg" // Default to JPEG
                        };

                        // Create a data URL
                        string dataUrl = $"data:{mimeType};base64,{responseObj.FileBytes}";

                        // Cache the data URL
                        CacheImageDataUrl(imagePath, dataUrl);

                        return dataUrl;
                    }
                    else
                    {
                        // Try to extract the file bytes using regex
                        var fileBytesMatch = Regex.Match(responseJson, @"""fileBytes""\s*:\s*""([^""]+)""");
                        if (fileBytesMatch.Success)
                        {
                            string fileBytes = fileBytesMatch.Groups[1].Value;

                            // Try to determine the file extension
                            var fileExtensionMatch = Regex.Match(responseJson, @"""fileExtension""\s*:\s*""([^""]+)""");
                            string fileExtension = fileExtensionMatch.Success ? fileExtensionMatch.Groups[1].Value : ".jpg";

                            // Determine the MIME type based on the file extension
                            string mimeType = fileExtension.ToLower() switch
                            {
                                ".jpg" => "image/jpeg",
                                ".jpeg" => "image/jpeg",
                                ".png" => "image/png",
                                ".gif" => "image/gif",
                                _ => "image/jpeg" // Default to JPEG
                            };

                            // Create a data URL
                            string dataUrl = $"data:{mimeType};base64,{fileBytes}";

                            // Cache the data URL
                            CacheImageDataUrl(imagePath, dataUrl);

                            return dataUrl;
                        }
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error parsing response as FileStorageHubResponse: {ex.Message}");
                }

                // If we couldn't parse the response, convert the raw bytes to a data URL
                string fallbackDataUrl = $"data:image/jpeg;base64,{Convert.ToBase64String(imageBytes)}";

                // Cache the data URL
                CacheImageDataUrl(imagePath, fallbackDataUrl);

                return fallbackDataUrl;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting image data URL: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                throw;
            }
        }

        public void CacheImageDataUrl(string imagePath, string dataUrl)
        {
            _imageCache[imagePath] = new ImageCacheItem
            {
                ImagePath = imagePath,
                DataUrl = dataUrl,
                CacheTime = DateTime.Now
            };

            Console.WriteLine($"Cached image {imagePath}");
        }

        public Dictionary<string, ImageCacheItem> GetImageCache()
        {
            return _imageCache;
        }
    }
}
