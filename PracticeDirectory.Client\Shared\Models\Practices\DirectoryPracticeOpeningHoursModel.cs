﻿namespace PracticeDirectory.Client.Shared.Models.Practices;

public class DirectoryPracticeOpeningHoursModel
{
	public int PracticeId { get; set; }
	public string ImportStatus { get; set; } = string.Empty;
	public string[] Days { get; set; } = ["", "MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY", "SATURDAY", "SUNDAY"];
	public string[] OpeningHours { get; set; } = new string[8];
	public string[] ClosingHours { get; set; } = new string[8];
	public bool[] IsClosed { get; set; } = new bool[8];	
}
