﻿using Microsoft.AspNetCore.Components.Authorization;
using PracticeDirectory.Client.Shared.Configuration;
using System.Text.Json;

namespace PracticeDirectory.Client.Shared.Extensions
{
	public static class AuthExtensions
	{
		private static ImpersonationSettings? _impersonationSettings;
		private static CommissioningTeamSettings? _commissioningTeamSettings;
		private static MarketingTeamSettings? _marketingTeamSettings;
		private static AdminTeamSettings _adminTeamSettings = new();
		private static CustomAttributesSettings? _customAttributesSettings;

		public static void InitializeAuthSettings(
			ImpersonationSettings impersonationSettings,
			CommissioningTeamSettings commissioningTeamSettings,
			MarketingTeamSettings marketingTeamSettings,
			AdminTeamSettings adminTeamSettings,
			CustomAttributesSettings customAttributesSettings)
		{
			_impersonationSettings = impersonationSettings;
			_commissioningTeamSettings = commissioningTeamSettings;
			_marketingTeamSettings = marketingTeamSettings;
			_adminTeamSettings = adminTeamSettings;
			_customAttributesSettings = customAttributesSettings;
		}

		public static string GetWindowsUserName(this AuthenticationState authState, string overrideuser)
        {
            string name = "Anonymous";

			if (!string.IsNullOrWhiteSpace(overrideuser))
			{
                name = overrideuser;
            }
            else if (_impersonationSettings == null || _impersonationSettings.Impersonate != "Yes")
            {
                var user = authState.User;
                if (user.Identity!.IsAuthenticated)
                {
                    name = user.Identity.Name!;
                }
            }
            else
            {
                name = _impersonationSettings.Username;  // Use the impersonated user.
            }
            return name;
        }

        public static string GetOriginalWindowsUserName(this AuthenticationState authState)
        {
			string name = "Anonymous";

          
                var user = authState.User;
			if (user.Identity!.IsAuthenticated)
			{
				name = user.Identity.Name!;
			}
           
            return name;
        }


        public static bool GetWindowsUserIsAuthenticated(this AuthenticationState authState)
		{
			return authState.User.Identity!.IsAuthenticated;
		}

		public static bool CheckCurrentUserIsInCommissioningTeam(this AuthenticationState authState)
		{
			string windowsUserName = "Anonymous";

			var user = authState.User;
			if (user.Identity!.IsAuthenticated)
			{
				windowsUserName = user.Identity.Name!;
			}

			string cleanedUserName = windowsUserName.Contains('\\') ? windowsUserName.Split('\\')[1] : windowsUserName;

			string commissioningTeamUserNames = _commissioningTeamSettings?.CommissioningTeamUserNames ?? string.Empty;

			List<string> commissioningTeamUserNamesList = ConvertUserNamesToList(commissioningTeamUserNames);

			return  (commissioningTeamUserNamesList.Count > 0 && commissioningTeamUserNamesList.Contains(cleanedUserName, StringComparer.OrdinalIgnoreCase));
		}

		public static bool CheckUserIsInCommissioningTeam(this AuthenticationState authState, string user)
		{
			if (string.IsNullOrWhiteSpace(user)) { return false; }

			string cleanedUserName = user.Contains('\\') ? user.Split('\\')[1] : user;

			string commissioningTeamUserNames = _commissioningTeamSettings?.CommissioningTeamUserNames ?? string.Empty;

			List<string> commissioningTeamUserNamesList = ConvertUserNamesToList(commissioningTeamUserNames);

			return (commissioningTeamUserNamesList.Count > 0 && commissioningTeamUserNamesList.Contains(cleanedUserName, StringComparer.OrdinalIgnoreCase));
		}

		public static bool CheckCurrentUserIsInMarketingTeam(this AuthenticationState authState)
		{
			string windowsUserName = "Anonymous";

			var user = authState.User;
			if (user.Identity!.IsAuthenticated)
			{
				windowsUserName = user.Identity.Name!;
			}

			string cleanedUserName = windowsUserName.Contains('\\') ? windowsUserName.Split('\\')[1] : windowsUserName;

			string marketingTeamUserNames = _marketingTeamSettings?.MarketingTeamUserNames ?? string.Empty;

			List<string> marketingTeamUserNamesList = ConvertUserNamesToList(marketingTeamUserNames);

			return (marketingTeamUserNamesList.Count > 0 && marketingTeamUserNamesList.Contains(cleanedUserName, StringComparer.OrdinalIgnoreCase));
		}

		public static bool CheckCurrentUserCanEditCustomAttributes(this AuthenticationState authState)
		{
			string windowsUserName = "Anonymous";

			var user = authState.User;
			if (user.Identity!.IsAuthenticated)
			{
				windowsUserName = user.Identity.Name!;
			}

			string cleanedUserName = windowsUserName.Contains('\\') ? windowsUserName.Split('\\')[1] : windowsUserName;

			string canEditAttributesUserNames = _customAttributesSettings?.CanEditeAttributes ?? string.Empty;

			List<string> canEditAttributesUserNamesList = ConvertUserNamesToList(canEditAttributesUserNames);

			return (canEditAttributesUserNamesList.Count > 0 && canEditAttributesUserNamesList.Contains(cleanedUserName, StringComparer.OrdinalIgnoreCase));
		}

		private static List<string> ConvertUserNamesToList(string userNames)
		{
			List<string> usersList = [];

			if (userNames.Contains('|'))
			{
				usersList = [.. userNames.Split('|')];
			}
			else
			{
				if (string.IsNullOrWhiteSpace(userNames))
				{
					return [];
				}

				usersList.Add(userNames.Trim());
			}

			return usersList;
		}

        public static int GetPracticeId(this AuthenticationState authState, IHttpClientFactory clientFactory, string overrideuser)
        {
            var userName = authState.GetWindowsUserName(overrideuser);
            var httpClient = clientFactory.CreateClient(ApiSettings.Api);


            var url = $"authorization/name?username={userName}";

            var responseMessage = httpClient.GetAsync(url).Result;  // Use .Result to wait synchronously for the response.
            int practiceId = 0;

            if (responseMessage.IsSuccessStatusCode)
            {
                var jsonString = responseMessage.Content.ReadAsStringAsync().Result; // Read the content synchronously.
                using var jsonDoc = JsonDocument.Parse(jsonString);
                if (jsonDoc.RootElement.TryGetProperty("practice_Id", out var practiceIdElement))
                {
                    practiceId = practiceIdElement.GetInt32();
                }
            }
            else
            {
                Console.WriteLine($"Failed to fetch Practice ID for user {userName}. Status: {responseMessage.StatusCode}");
            }

            return practiceId;
        }

		public static bool UserIsPracticeManagerForPracticeId(int practiceId, int currentUserPracticeId)
		{
			if (practiceId != 0 && currentUserPracticeId != 0)
				return practiceId == currentUserPracticeId;

			return false;
		}

		public static bool IsUserAdmin(string user)
		{
			return _adminTeamSettings
				.GetUserList()
				.Contains(user.Split('\\').Last(), StringComparer.OrdinalIgnoreCase);
		}
	}
}
