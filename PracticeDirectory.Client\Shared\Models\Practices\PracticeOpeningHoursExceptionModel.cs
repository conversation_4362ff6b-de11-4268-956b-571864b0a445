using System.ComponentModel.DataAnnotations;

namespace PracticeDirectory.Client.Shared.Models.Practices;

public class PracticeOpeningHoursExceptionModel
{
    public int Id { get; set; }
    public int PracticeId { get; set; }

    [Required(ErrorMessage = "A description is required.")]
    [StringLength(200, ErrorMessage = "Description cannot be longer than 200 characters.")]
    public string Description { get; set; } = string.Empty;

    [Required(ErrorMessage = "Start date is required.")]
    public DateTime? StartDate { get; set; }

    [Required(ErrorMessage = "End date is required.")]
    public DateTime? EndDate { get; set; }

    public bool IsActive { get; set; }
    public string? CreatedBy { get; set; }
    public string? LastUpdatedBy { get; set; }
    public DateTime CreatedDate { get; set; }
    public DateTime LastUpdateDate { get; set; }

    public string DateRangeDisplay
    {
        get
        {
            if (StartDate.HasValue && EndDate.HasValue)
            {
                return StartDate.Value.Date == EndDate.Value.Date
                    ? StartDate.Value.ToString("dd MMM yyyy")
                    : $"{StartDate.Value:dd MMM yyyy} - {EndDate.Value:dd MMM yyyy}";
            }
            return "No date set";
        }
    }
}