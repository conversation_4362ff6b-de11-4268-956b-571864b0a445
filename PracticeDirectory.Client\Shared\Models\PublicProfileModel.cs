﻿namespace PracticeDirectory.Client.Shared.Models;

public class PublicProfileModel
{
    public int Id { get; set; }
    public string? PersonId { get; set; }
	public string? PersonRole { get; set; }
	public string? GDCNumber { get; set; }
    public string? DisplayNameAs { get; set; }
    public string? Bio { get; set; }
    public string? DraftBio { get; set; }
    public int? Status { get; set; }
    public DateTime? CreatedDate { get; set; }
    public DateTime? LastUpdateDate { get; set; }
    public string? LastUpdateBy { get; set; }
    public string? CreatedBy { get; set; }
    public int? Practice { get; set; }
}
