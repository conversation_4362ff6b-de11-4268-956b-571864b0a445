﻿@page "/practice/{PracticeId:int}/attributes"
@using Microsoft.AspNetCore.Components.Authorization
@using PracticeDirectory.Client.Service
@using PracticeDirectory.Client.Shared.Configuration
@using PracticeDirectory.Client.Shared.Extensions
@using PracticeDirectory.Client.Shared.Models
@using PracticeDirectory.Client.Shared
@using PracticeDirectory.Client.Components
@using System.Text.Json
@using System.Linq
@using System.Net.Http.Json
@using Havit.Blazor.Components.Web.Bootstrap
@using Havit.Blazor.Components.Web
@using PracticeDirectory.Client.Shared.Models.Practices
@using Microsoft.Extensions.Options
@inject IHttpClientFactory ClientFactory
@inject IJSRuntime JSRuntime
@inject IHxMessengerService HxMessengerProvider
@inject NavigationManager Navigation
@inject IConfiguration Configuration
@inject UserContextService UserContext
@inject ILogger<PracticeCustomAttributes> Logger

<HxPageTitle Title="Practice Custom Attributes" />

@if (isLoading)
{
    <p><em>Loading attributes...</em></p>
    <HxSpinner />
}
else if (AttributeGroups == null || !HasAnyAttributesToDisplay())
{
    <HxAlert Color="ThemeColor.Warning">Could not load attribute data or configuration.</HxAlert>
}
else
{
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="mb-0">Practice Custom Attributes</h1>
        <div>
            @if (IsEditMode)
            {
                <HxButton Color="ThemeColor.Success" OnClick="HandleSaveClick" Enabled="!isSaving" CssClass="me-2" Loading="isSaving">
                    <i class="bi bi-save"></i> <span class="ms-1">Save Changes</span>
                </HxButton>
                <HxButton Color="ThemeColor.Secondary" OnClick="HandleCancelClick" Enabled="!isSaving">Cancel</HxButton>
            }
            else if (CanEditAttributes)
            {
                <HxButton Color="ThemeColor.Primary" OnClick="HandleEditClick">
                    <i class="bi bi-pencil-square"></i> <span class="ms-1">Edit</span>
                </HxButton>
            }
        </div>
    </div>

    @if (!CanEditAttributes)
    {
        <div class="alert alert-info">
            <p>You don't have permission to edit custom attributes. You can view the attributes but cannot make changes. Only authorized users can edit custom attributes.</p>
        </div>
    }

    <HxAccordion StayOpen="true">
        @foreach (var group in AttributeGroups)
        {
            var groupName = group.Key;
            var attributesInGroup = GetAttributesForGroup(group.Value);

            <HxAccordionItem>
                <HeaderTemplate>
                    <h5 class="mb-0">@groupName</h5>
                </HeaderTemplate>
                <BodyTemplate>
                    @foreach (var attr in attributesInGroup)
                    {
                        <div class="row py-2 border-bottom align-items-center">
                            <div class="col-md-4 fw-bold">@attr.CustomAttributeName</div>
                            <div class="col-md-8">
                                @if (IsEditMode)
                                {
                                    switch (attr.CustomAttributeTypeName)
                                    {
                                        case "Yes/No":
                                            <HxCheckbox @bind-Value="attr.IsYes" />
                                            break;
                                        case "Number":
                                            var numberValue = GetNumberValue(attr.Value);
                                            <HxInputNumber @bind-Value="numberValue" TValue="decimal?" OnValueChanged="@((decimal? value) => attr.Value = value)" />
                                            break;
                                        case "Text":
                                        default:
                                            <HxInputText @bind-Value="attr.StringValue" />
                                            break;
                                    }
                                }
                                else
                                {
                                    <span class="@(string.IsNullOrEmpty(attr.Value?.ToString()) ? "text-muted fst-italic" : "")">
                                        @GetFormattedValue(attr)
                                    </span>
                                }
                            </div>
                        </div>
                    }
                </BodyTemplate>
            </HxAccordionItem>
        }

        @{
            var uncategorizedAttributes = GetUncategorizedAttributes();
        }
        @if (uncategorizedAttributes.Any())
        {
            <HxAccordionItem>
                <HeaderTemplate>
                    <h5 class="mb-0 text-muted">
                        <i class="bi bi-exclamation-triangle me-2"></i>Uncategorized Attributes
                    </h5>
                </HeaderTemplate>
                <BodyTemplate>
                    <div class="alert alert-warning mb-3">
                        <small>
                            <i class="bi bi-info-circle me-1"></i>
                            These attributes are not configured in the system settings and may need to be added to a category.
                        </small>
                    </div>
                    @foreach (var attr in uncategorizedAttributes)
                    {
                        <div class="row py-2 border-bottom align-items-center">
                            <div class="col-md-4 fw-bold">@attr.CustomAttributeName</div>
                            <div class="col-md-8">
                                @if (IsEditMode)
                                {
                                    switch (attr.CustomAttributeTypeName)
                                    {
                                        case "Yes/No":
                                            <HxCheckbox @bind-Value="attr.IsYes" />
                                            break;
                                        case "Number":
                                            var numberValue = GetNumberValue(attr.Value);
                                            <HxInputNumber @bind-Value="numberValue" TValue="decimal?" OnValueChanged="@((decimal? value) => attr.Value = value)" />
                                            break;
                                        case "Text":
                                        default:
                                            <HxInputText @bind-Value="attr.StringValue" />
                                            break;
                                    }
                                }
                                else
                                {
                                    <span class="@(string.IsNullOrEmpty(attr.Value?.ToString()) ? "text-muted fst-italic" : "")">
                                        @GetFormattedValue(attr)
                                    </span>
                                }
                            </div>
                        </div>
                    }
                </BodyTemplate>
            </HxAccordionItem>
        }
    </HxAccordion>
}
@code {
    [Parameter]
    public int PracticeId { get; set; }

    [CascadingParameter]
    private Task<AuthenticationState> authenticationStateTask { get; set; } = default!;

    private List<PracticeAttributeDto> Attributes { get; set; } = new();
    private AttributeGroupSettings AttributeGroups { get; set; }

    private bool IsEditMode { get; set; } = false;
    private bool isLoading = true;
    private bool isSaving = false;
    private string originalAttributesAsJson;
    private string userName = string.Empty;
    private bool canEditAttributes = false;

    private bool CanEditAttributes => canEditAttributes;

    private HttpClient CreateAuthenticatedClient()
    {
        var httpClient = ClientFactory.CreateClient(ApiSettings.Api);
        httpClient.DefaultRequestHeaders.Remove("X-ORIG-NAME"); // Ensure no duplicates
        httpClient.DefaultRequestHeaders.Add("X-ORIG-NAME", userName);
        return httpClient;
    }

    protected override async Task OnInitializedAsync()
    {
        var authState = await authenticationStateTask;
        userName = authState.GetWindowsUserName(UserContext.OverrideUsername);

        canEditAttributes = authState.CheckCurrentUserCanEditCustomAttributes();

        if (string.IsNullOrWhiteSpace(UserContext.OverrideUsername))
        {
            var storedOverride = await JSRuntime.InvokeAsync<string>("localStorage.getItem", "overrideUsername");
            if (!string.IsNullOrWhiteSpace(storedOverride))
            {
                UserContext.OverrideUsername = storedOverride;
            }
        }

        var settings = Configuration.GetSection("CustomAttributesSettings").Get<CustomAttributesSettings>();
        if (settings?.AttributeGroupSettings == null)
        {
            Logger.LogError("AttributeGroupSettings not found in configuration.");
            HxMessengerProvider.AddWarning("Configuration Error", "Attribute groups are not configured.");
            _ = Task.Delay(3000).ContinueWith(_ => InvokeAsync(() => HxMessengerProvider.Clear()));
            isLoading = false;
            return;
        }
        AttributeGroups = settings.AttributeGroupSettings;
        await LoadAttributes();
    }

    private async Task LoadAttributes()
    {
        isLoading = true;
        try
        {
            var httpClient = CreateAuthenticatedClient();
            var response = await httpClient.GetAsync($"practicecustomattributes/practice/{PracticeId}");

            if (response.IsSuccessStatusCode)
            {
                var result = await response.Content.ReadFromJsonAsync<PracticeAttributeResponse>();
                if (result?.Records != null)
                {
                    Attributes = result.Records;
                }
                else
                {
                    Attributes = new List<PracticeAttributeDto>();
                    HxMessengerProvider.AddWarning("Warning", "No custom attributes found for this practice.");
                    _ = Task.Delay(3000).ContinueWith(_ => InvokeAsync(() => HxMessengerProvider.Clear()));
                }
            }
            else
            {
                Logger.LogError("Failed to load custom attributes. Status code: {StatusCode}", response.StatusCode);
                HxMessengerProvider.AddWarning("Error", "Could not load custom attributes. Please try again later.");
                _ = Task.Delay(3000).ContinueWith(_ => InvokeAsync(() => HxMessengerProvider.Clear()));
                Attributes = new List<PracticeAttributeDto>();
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Failed to load custom attributes for PracticeId {PracticeId}", PracticeId);
            HxMessengerProvider.AddWarning("Error", "Could not load custom attributes.");
            _ = Task.Delay(3000).ContinueWith(_ => InvokeAsync(() => HxMessengerProvider.Clear()));
            Attributes = new List<PracticeAttributeDto>();
        }
        finally
        {
            isLoading = false;
        }
    }

    private List<PracticeAttributeDto> GetAttributesForGroup(List<string> attributeCodes)
    {
        // Orders the attributes based on their sequence in appsettings.json
        return attributeCodes
            .Select(code => Attributes.FirstOrDefault(a => a.CustomAttributeCode == code))
            .Where(attr => attr != null)
            .ToList();
    }

    private void HandleEditClick()
    {
        originalAttributesAsJson = JsonSerializer.Serialize(Attributes);
        IsEditMode = true;
    }

    private void HandleCancelClick()
    {
        Attributes = JsonSerializer.Deserialize<List<PracticeAttributeDto>>(originalAttributesAsJson);
        IsEditMode = false;
    }

    private async Task HandleSaveClick()
    {
        isSaving = true;
        try
        {
            var httpClient = CreateAuthenticatedClient();
            var successCount = 0;
            var errorCount = 0;

            // Deserialize the original attributes to compare with current values
            var originalAttributes = JsonSerializer.Deserialize<List<PracticeAttributeDto>>(originalAttributesAsJson);
            var changedAttributes = new List<PracticeAttributeDto>();

            // Find attributes that have changed
            foreach (var currentAttr in Attributes)
            {
                var originalAttr = originalAttributes?.FirstOrDefault(a => a.CustomAttributeCode == currentAttr.CustomAttributeCode);
                if (originalAttr == null || !ValuesAreEqual(currentAttr.Value, originalAttr.Value))
                {
                    changedAttributes.Add(currentAttr);
                }
            }

            if (!changedAttributes.Any())
            {
                HxMessengerProvider.AddWarning("Info", "No changes detected.");
                _ = Task.Delay(3000).ContinueWith(_ => InvokeAsync(() => HxMessengerProvider.Clear()));
                IsEditMode = false;
                return;
            }

            foreach (var attr in changedAttributes)
            {
                var payload = new
                {
                    practiceId = PracticeId,
                    customAttributeCode = attr.CustomAttributeCode,
                    customAttributeName = attr.CustomAttributeName,
                    customAttributeTypeName = attr.CustomAttributeTypeName,
                    value = attr.Value?.ToString() ?? "",
                    notes = attr.Notes ?? ""
                };

                var response = await httpClient.PostAsJsonAsync("practicecustomattributes/upsert", payload);

                if (response.IsSuccessStatusCode)
                {
                    successCount++;
                }
                else
                {
                    errorCount++;
                    Logger.LogError("Failed to upsert attribute {AttributeCode}. Status code: {StatusCode}", 
                        attr.CustomAttributeCode, response.StatusCode);
                }
            }

            if (errorCount == 0)
            {
                HxMessengerProvider.AddWarning("Success", $"Attributes saved successfully!");
                IsEditMode = false;

                _ = Task.Delay(3000).ContinueWith(_ => InvokeAsync(() => HxMessengerProvider.Clear()));
            }
            else if (successCount > 0)
            {
                HxMessengerProvider.AddWarning("Warning", 
                    $"{successCount} attributes saved successfully, but {errorCount} failed to save.");
                _ = Task.Delay(3000).ContinueWith(_ => InvokeAsync(() => HxMessengerProvider.Clear()));
            }
            else
            {
                HxMessengerProvider.AddWarning("Error", "Failed to save any attributes. Please try again.");
                _ = Task.Delay(3000).ContinueWith(_ => InvokeAsync(() => HxMessengerProvider.Clear()));
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Failed to save practice attributes for PracticeId {PracticeId}", PracticeId);
            HxMessengerProvider.AddWarning("Error", "Failed to save changes. Please try again.");
            _ = Task.Delay(3000).ContinueWith(_ => InvokeAsync(() => HxMessengerProvider.Clear()));
        }
        finally
        {
            isSaving = false;
        }
    }

    private bool ValuesAreEqual(object value1, object value2)
    {
        if (value1 == null && value2 == null)
            return true;
        if (value1 == null || value2 == null)
            return false;

        var string1 = value1.ToString() ?? "";
        var string2 = value2.ToString() ?? "";
        
        return string1.Equals(string2, StringComparison.OrdinalIgnoreCase);
    }

    private string GetFormattedValue(PracticeAttributeDto attr)
    {
        if (attr.Value == null || string.IsNullOrWhiteSpace(attr.Value.ToString()))
        {
            return "Not Set";
        }
        if (attr.CustomAttributeTypeName == "Yes/No")
        {
            return attr.Value.ToString() == "Y" ? "Yes" : "No";
        }
        return attr.Value.ToString();
    }

    private decimal? GetNumberValue(object value)
    {
        if (value == null)
            return null;

        if (value is JsonElement jsonElement)
        {
            if (jsonElement.ValueKind == JsonValueKind.Number)
            {
                return jsonElement.GetDecimal();
            }
            else if (jsonElement.ValueKind == JsonValueKind.String)
            {
                return decimal.TryParse(jsonElement.GetString(), out decimal parsedValue) ? parsedValue : (decimal?)null;
            }
        }
        else if (value is decimal decimalValue)
        {
            return decimalValue;
        }
        else if (value is int intValue)
        {
            return intValue;
        }
        else if (value is double doubleValue)
        {
            return (decimal)doubleValue;
        }
        else if (value is string stringValue)
        {
            return decimal.TryParse(stringValue, out decimal parsedValue) ? parsedValue : (decimal?)null;
        }

        return null;
    }

    private List<PracticeAttributeDto> GetUncategorizedAttributes()
    {
        // Get all configured attribute codes from all groups
        var configuredAttributeCodes = AttributeGroups
            .SelectMany(group => group.Value)
            .ToHashSet(StringComparer.OrdinalIgnoreCase);

        // Return attributes that are not in any configured group
        var uncategorized = Attributes
            .Where(attr => !configuredAttributeCodes.Contains(attr.CustomAttributeCode))
            .OrderBy(attr => attr.CustomAttributeName)
            .ToList();

        if (uncategorized.Any())
        {
            Logger.LogInformation("Found {Count} uncategorized attributes: {AttributeCodes}", 
                uncategorized.Count, 
                string.Join(", ", uncategorized.Select(a => a.CustomAttributeCode)));
        }

        return uncategorized;
    }

    private bool HasAnyAttributesToDisplay()
    {
        if (!Attributes.Any())
            return false;
        var hasCategorizedAttributes = AttributeGroups
            .SelectMany(group => group.Value)
            .Any(code => Attributes.Any(attr => attr.CustomAttributeCode == code));

        var hasUncategorizedAttributes = GetUncategorizedAttributes().Any();

        return hasCategorizedAttributes || hasUncategorizedAttributes;
    }
}