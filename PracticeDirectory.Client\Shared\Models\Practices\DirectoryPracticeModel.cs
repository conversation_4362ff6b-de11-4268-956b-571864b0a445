﻿namespace PracticeDirectory.Client.Shared.Models.Practices;

public class DirectoryPracticeModel
{
	public int Id { get; set; }
	public string PracticeName { get; set; } = string.Empty;
	public string WebsiteName { get; set; } = string.Empty;
	public string ImageUrl { get; set; } = string.Empty;
	public string AboutUs { get; set; } = string.Empty;
	public bool IsActive { get; set; }
	public string AreaName { get; set; } = string.Empty;
	public string RegionName { get; set; } = string.Empty;
	public DirectoryPracticePostalAddressModel? PostalAddress { get; set; }
}
