﻿@page "/practice/editContractedHours/{PracticeId:int}"
@using Microsoft.AspNetCore.Components.Authorization
@using System.ComponentModel.DataAnnotations
@using PracticeDirectory.Client.Service
@using PracticeDirectory.Client.Shared.Extensions
@using PracticeDirectory.Client.Shared.Models.Practices
@using PracticeDirectory.Client.Shared.Models
@using Havit.Blazor.Components.Web.Bootstrap
@inject IHttpClientFactory ClientFactory
@inject UserContextService UserContext
@inject NavigationManager Navigation

<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css" />

<div style="max-width: 960px; margin: 0 1rem;">

    <h3>Update Contracted Hours</h3>
    <div class="text-end mb-2">
        <a href="practice/@PracticeId" class="btn btn-secondary" style="max-width: 240px; line-height: 30px;">
            Back To Practice
        </a>
    </div>
 @if (isAdmin|| checkCurrentUserIsInCommissioningTeam)
   {    
    @if (isLoading)
    {
        <p><em>Loading...</em></p>
    }

    else
    {
        @if (!string.IsNullOrEmpty(errorMessage))
        {
            <div class="alert alert-danger" role="alert">@errorMessage</div>
        }
        @if (!string.IsNullOrEmpty(validationErrorMessage))
        {
            <div class="alert alert-danger" role="alert">@validationErrorMessage</div>
        }


        @if (!string.IsNullOrEmpty(successMessage))
        {
            <div class="alert alert-success" role="alert">@successMessage</div>
        }

        <div class="table-responsive">
            <table class="table table-striped align-middle">
                <thead class="table-light">
                    <tr>
                        <th scope="col" style="width: 15%;">Day</th>
                        <th scope="col">Opening Time</th>
                        <th scope="col">Closing Time</th>
                        <th scope="col" class="text-center">Closed</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var day in dailyHours)
                    {
                        <tr @key="day.DayOfWeekCode">
                            <th scope="row">@day.DayName</th>
                            <td>
                                <HxSelect TItem="TimeSlotItemModel"
                                          TValue="string"
                                          Data="@timeSlotList"
                                          @bind-Value="day.OpeningTime"
                                          Enabled="!day.IsClosed"
                                          Nullable="true"
                                          NullText="--"
                                          ValueSelector="@(item => item.Name)"
                                          TextSelector="@(item => item.Name)" />

                            </td>
                            <td>
                                <HxSelect TItem="TimeSlotItemModel"
                                          TValue="string"
                                          Data="@timeSlotList"
                                          @bind-Value="day.ClosingTime"
                                          Enabled="!day.IsClosed"
                                          Nullable="true"
                                          NullText="--"
                                          ValueSelector="@(item => item.Name)"
                                          TextSelector="@(item => item.Name)" />


                            </td>
                            <td class="text-center">
                                <HxCheckbox Value="day.IsClosed"
                                            ValueChanged="(value) => OnIsClosedChanged(day, value)"
                                            ValueExpression="@(() => day.IsClosed)" />

                            </td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
        <div class="row mt-3">
        @if (showInProgressWarningMessage)
        {
            <div class="alert alert-warning" role="alert">
                Requested Contracted Hours changes are in progress. This can take up to 24 hours.
            </div>
        }
    </div>

        <div class="mt-4">
            <button class="btn btn-primary" @onclick="HandleSaveAll" style="max-width: 240px; margin-right: 36px; margin-bottom: 8px;">Save Changes</button>
            <a href="practice/@PracticeId" class="btn btn-secondary" style="max-width: 240px; margin-bottom: 8px; line-height: 30px;">Back To Practice</a>
        </div>
        }
    }
    else
    {
         <div class="alert alert-warning" role="alert">
             You are not authorised to view this page...
         </div>
    }
</div>


@code {
    [Parameter] public int PracticeId { get; set; }

    private List<DailyContractedHoursViewModel> dailyHours = new();
    private List<TimeSlotItemModel> timeSlotList = new();
    private string userName = string.Empty;
    private string? errorMessage;
    private string? successMessage;
    private bool isLoading = true;
    private string? validationErrorMessage;
    private bool showInProgressWarningMessage = false;
    private bool checkCurrentUserIsInCommissioningTeam = false;
    private bool isAdmin = false;

    private readonly string[] days = ["", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"];

    [CascadingParameter]
    private Task<AuthenticationState> authenticationStateTask { get; set; } = default!;

    protected override async Task OnInitializedAsync()
    {
        var authState = await authenticationStateTask;
        userName = authState.GetWindowsUserName(UserContext.OverrideUsername);
        checkCurrentUserIsInCommissioningTeam = authState.CheckCurrentUserIsInCommissioningTeam();
        isAdmin = AuthExtensions.IsUserAdmin(authState.GetOriginalWindowsUserName());
        LoadTimeSlots();
        await LoadContractedHours();
    }

    private void LoadTimeSlots()
    {
        timeSlotList.Add(new TimeSlotItemModel { Id = 0, Name = "00:00" });
        for (int i = 1; i < 96; i++)
        {
            var time = TimeSpan.FromMinutes(i * 15);
            timeSlotList.Add(new TimeSlotItemModel { Id = i, Name = time.ToString(@"hh\:mm") });
        }
    }

private async Task LoadContractedHours()
{
    isLoading = true;
    try
    {
        var httpClient = ClientFactory.CreateClient("Api");
        httpClient.DefaultRequestHeaders.Add("X-ORIG-NAME", userName);

        var response = await httpClient.GetAsync($"practicecontractedhours/{PracticeId}");
        response.EnsureSuccessStatusCode();

        var apiResult = await response.Content.ReadFromJsonAsync<ApiResponse<List<PracticeContractedHoursDto>>>();
        var contractedHours = apiResult?.Records ?? new List<PracticeContractedHoursDto>();

        if (contractedHours.Count() > 0)
            {
                var firstItem = contractedHours.FirstOrDefault();
                if (firstItem?.In_Progress == true)
                {
                    showInProgressWarningMessage = true;
                }
            }

        dailyHours.Clear();
        for (int i = 1; i <= 7; i++)
        {
            var dayModel = contractedHours.FirstOrDefault(c => c.Day_Of_Week == i);
            var isClosed = dayModel?.Is_Closed == 1;

            dailyHours.Add(new DailyContractedHoursViewModel
            {
                DayOfWeekCode = i.ToString(),
                DayName = days[i],
                OpeningTime = isClosed ? null : dayModel?.Opening_Time_Name,
                ClosingTime = isClosed ? null : dayModel?.Closing_Time_Name,
                IsClosed = isClosed,
            });
        }
    }
    catch (Exception ex)
    {
        errorMessage = $"Failed to load contracted hours. Error: {ex.Message}";
    }
    finally
    {
        isLoading = false;
    }
}


    private void OnIsClosedChanged(DailyContractedHoursViewModel day, bool isClosed)
    {
        day.IsClosed = isClosed;

        if (isClosed)
        {
            day.OpeningTime = null;
            day.ClosingTime = null;
        }
        else
        {
            day.OpeningTime ??= "09:00";
            day.ClosingTime ??= "17:00";
        }

        StateHasChanged();
    }


    private bool ValidateTimes()
    {
        validationErrorMessage = null;

        foreach (var day in dailyHours)
        {
            if (!day.IsClosed)
            {
                if (string.IsNullOrEmpty(day.OpeningTime) || string.IsNullOrEmpty(day.ClosingTime))
                {
                    validationErrorMessage = $"Opening and Closing times must be set for {day.DayName}.";
                    return false;
                }

                // Parse times
                if (TimeSpan.TryParse(day.OpeningTime, out var opening) && TimeSpan.TryParse(day.ClosingTime, out var closing))
                {
                    if (opening >= closing)
                    {
                        validationErrorMessage = $"On {day.DayName}, Opening Time must be less than Closing Time.";
                        return false;
                    }
                }
                else
                {
                    validationErrorMessage = $"Invalid time format for {day.DayName}.";
                    return false;
                }
            }
        }

        return true;
    }

    private async Task HandleSaveAll()
    {
        ClearMessages();

        if (!ValidateTimes())
        {
            // Validation failed — display message, don't save
            return;
        }

        try
        {
            var httpClient = ClientFactory.CreateClient("Api");
            httpClient.DefaultRequestHeaders.Add("X-ORIG-NAME", userName);

            var model = new PracticeContractedHoursToDatabaseCommandModel
            {
                PracticeId = PracticeId,
                LastUpdatedBy = userName,
                LastUpdateDate = DateTime.Now,
                OpeningHours = new string[8],
                ClosingHours = new string[8],
                IsClosed = new bool[8]
            };

            foreach (var day in dailyHours)
            {
                int index = int.Parse(day.DayOfWeekCode);

                model.OpeningHours[index] = day.OpeningTime ?? string.Empty;
                model.ClosingHours[index] = day.ClosingTime ?? string.Empty;
                model.IsClosed[index] = day.IsClosed;
            }

            var response = await httpClient.PostAsJsonAsync($"practicecontractedhours", model);

            if (!response.IsSuccessStatusCode)
            {
                errorMessage = $"Save failed. Server responded with: {response.StatusCode} - {response.ReasonPhrase}";
                return;
            }

            successMessage = "Saved successfully waiting for approval";
            await LoadContractedHours();
            StateHasChanged();
        }
        catch (Exception ex)
        {
            errorMessage = $"An error occurred while saving: {ex.Message}";
        }
    }
     private class ApiResponse<T>
    {
        public T? Records { get; set; }
    }

    private void ClearMessages()
    {
        errorMessage = null;
        successMessage = null;
    }

    public class DailyContractedHoursViewModel
    {
        public string? Code { get; set; }
        public string DayOfWeekCode { get; set; } = default!;
        public string DayName { get; set; } = default!;
        public string? OpeningTime { get; set; }
        public string? ClosingTime { get; set; }
        public bool IsClosed { get; set; }
    }


}