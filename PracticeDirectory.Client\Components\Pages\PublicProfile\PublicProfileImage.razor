@page "/practice/{PracticeId:int}/PublicProfileImage/{PersonId}"
@using Microsoft.AspNetCore.Components.Authorization
@using PracticeDirectory.Client.Service
@using PracticeDirectory.Client.Shared.Configuration
@using PracticeDirectory.Client.Shared.Extensions
@using PracticeDirectory.Client.Shared.Models
@using PracticeDirectory.Client.Shared
@using PracticeDirectory.Client.Components
@using System.Text.Json
@using System.Text
@using System.Linq
@using System.IO
@using System.Net.Http.Json
@using System.Text.RegularExpressions
@using Havit.Blazor.Components.Web.Bootstrap
@inject IHttpClientFactory ClientFactory
@inject IJSRuntime JSRuntime
@inject IHxMessengerService HxMessengerProvider
@inject IFileStorageHubService FileStorageHubService
@inject PracticeContextService PracticeContext
@inject UserContextService UserContext
@inject NavigationManager Navigation

<div class="container">

    <!-- Public Profile Image and Back To Practice Side-by-Side -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h3 class="mt-3">Clinician Public profile Image</h3>

        <!-- Back To Practice (on the right) -->
        <button @onclick="BackToPractice" class="btn btn-secondary" style="max-width: 240px;">Back to Practice</button>
    </div>

    <div class="card shadow-sm mb-4" style="max-width: 1000px;margin-left: 0;">
        <div class="card-header bg-white border-bottom">
            <h3 class="mb-0">Photo Instructions</h3>
        </div>
        <div class="card-body" style="font-size: 0.9rem; padding: 1rem;">
            <div class="row">
                <div class="col-md-6 mb-2" style="font-size: 0.85rem; line-height: 1.2;">
                    <p>
                        People forget that a portrait without a REAL expression does not connect with patients, they respond to genuine emotion and not posed, cheesy smiles... be natural.
                    </p>
                    <p>
                        Take the shot at the highest resolution possible, anything bigger than 5MP is fine, most smartphones on the highest setting will do the job, but no selfies!! get someone else to take it or you may find your features distort.
                    </p>
                    <p>
                        Your portrait will be converted to black and white and part of the image will be treated with a teal selective colour.
                    </p>
                </div>
                <div class="col-md-6 mb-2" style="font-size: 0.85rem; line-height: 1.2;">
                    <p>
                        Have your portrait taken against a white background, a white wall should do, stand a foot away so any shadows soften, just watch your highlights aren't too bright!! don't shoot against a window, have any natural light falling on your face.
                    </p>
                    <p>
                        Don't stand straight on to the camera, turn slightly, bring your forehead towards the camera and your chin slightly out.
                    </p>
                    <p>
                        Include both shoulders, fill the frame but dont crop heads or arms, we'll crop the image to be consistent with our style the more image we have the better, to be safe shoot portrait.
                    </p>
                    <p>
                        If in doubt imitate the poses below and above all else relax!
                    </p>
                </div>
                <div class="d-flex justify-content-center gap-3">
                    <figure class="m-0" style="max-width: 30%;">
                        <img class="image_border" src="/Images/icon_portrait_1.png" alt="Portrait Pose 1" />
                    </figure>
                    <figure class="m-0" style="max-width: 30%;">
                        <img class="image_border" src="/Images/icon_portrait_2.png" alt="Portrait Pose 2" />
                    </figure>
                    <figure class="m-0" style="max-width: 30%;">
                        <img class="image_border" src="/Images/icon_portrait_3.png" alt="Portrait Pose 3" />
                    </figure>
                </div>
                <b>By submitting the form you are consenting for the information provided within the Public Profile section to be displayed on mydentist.co.uk website.</b>
            </div>
        </div>
    </div>




    <div class="row mb-5">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5>Current Image</h5>
                </div>
                <div class="card-body">

                    <div class="text-center mb-4">
                        @if (currentProfileImage != null)
                        {
                            <div class="position-relative">
                                <img src="@currentProfileImage.FullImageUrl" alt="@currentProfileImage.AltText" class="img-fluid rounded profile-image" />
                                @if (isImageUploaded && uploadedImageId.HasValue && uploadedImageId.Value == currentProfileImage.Id)
                                {
                                    <button type="button" class="btn-close position-absolute top-0 end-0 bg-light rounded-circle m-1"
                                    @onclick="@(async () => await RemoveCurrentImage())" aria-label="Remove image"></button>
                                }
                            </div>
                        }
                        else
                        {
                            <div class="image-placeholder">
                                <span>Select an image from the carousel below</span>
                            </div>
                        }
                    </div>


                    <div class="mb-4">
                        @if (availableImages.Count > 0)
                        {
                            <div class="thumbnail-gallery">
                                <div class="row g-2">
                                    @foreach (var image in availableImages)
                                    {
                                        <div class="col-3 col-md-2">
                                            @if (currentProfileImage?.Id == image.Id)
                                            {
                                                <div class="thumbnail-container selected" @onclick="() => SelectImageForView(image)">
                                                    <img src="@image.ThumbnailUrl" alt="@image.AltText" class="thumbnail-image" />
                                                    <div class="selected-indicator"></div>
                                                </div>
                                            }
                                            else
                                            {
                                                <div class="thumbnail-container" @onclick="() => SelectImageForView(image)">
                                                    <img src="@image.ThumbnailUrl" alt="@image.AltText" class="thumbnail-image" />
                                                </div>
                                            }
                                        </div>
                                    }
                                </div>
                            </div>
                        }
                        else
                        {
                            <div class="alert alert-info">No images available</div>
                        }
                    </div>


                    <div class="text-center">
                        @if (isPracticeManager)
                        {
                            @if (isImageUploaded)
                            {
                                <HxButton Text="Submit for Approval" Color="ThemeColor.Success" OnClick="@(async () => await SubmitForApproval())" />
                            }
                            else
                            {
                                <HxButton Text="Upload / Update" Color="ThemeColor.Primary" OnClick="@(async () => await OpenUploadModal())" />
                            }
                        }
                    </div>
                </div>
            </div>
        </div>
    </div>


    <div class="row mb-3">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h4>Image History</h4>
                @if (isPracticeManager && isMarketingTeamMember)
                {
                    <HxButtonGroup>
                        @if (isPracticeManager)
                        {
                            <HxButton Text="PM View" Color="@(UserRole == "PM" ? ThemeColor.Primary : ThemeColor.Secondary)"
                            OnClick="@(() => UserRole = "PM")" />
                        }
                        @if (isMarketingTeamMember)
                        {
                            <HxButton Text="Marketing View" Color="@(UserRole == "Marketing" ? ThemeColor.Primary : ThemeColor.Secondary)"
                            OnClick="@(() => UserRole = "Marketing")" />
                        }
                    </HxButtonGroup>
                }
            </div>
        </div>
    </div>

    @if (UserRole == "PM" && isPracticeManager)
    {
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-light d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">PM View</h5>
                        <HxButton Text="Refresh" Color="ThemeColor.Secondary" Size="ButtonSize.Small" OnClick="@(async () => await LoadPracticeImages())" />
                    </div>
                    <div class="card-body">
                        @if (ImageHistory.Any())
                        {
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>No</th>
                                            <th>Created Date</th>
                                            <th>Created By</th>
                                            <th>Status</th>
                                            <th>Image Type</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach (var item in ImageHistory.OrderByDescending(h => h.CreatedDate))
                                        {
                                            <tr>
                                                <td>@item.Id</td>
                                                <td>@item.CreatedDate.ToShortDateString()</td>
                                                <td>@item.CreatedBy</td>
                                                <td>@item.Status</td>
                                                <td>@(item.ImageType ?? "N/A")</td>
                                                <td>
                                                    <HxButton Text="View Image" Color="ThemeColor.Secondary" Size="ButtonSize.Small" OnClick="@(async () => await OpenViewModal(item))" />
                                                </td>
                                            </tr>
                                        }
                                    </tbody>
                                </table>
                            </div>
                        }
                        else
                        {
                            <div class="alert alert-info">
                                No history records available. Upload and submit images to see them in the history.
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>
    }
    else if (UserRole == "Marketing" && isMarketingTeamMember)
    {
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-light d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Marketing View</h5>
                        <HxButton Text="Refresh" Color="ThemeColor.Secondary" Size="ButtonSize.Small" OnClick="@(async () => await LoadPracticeImages())" />
                    </div>
                    <div class="card-body">
                        @if (ImageHistory.Any())
                        {
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>No</th>
                                            <th>Created Date</th>
                                            <th>Created By</th>
                                            <th>Status</th>
                                            <th>Image Type</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach (var item in ImageHistory.OrderByDescending(h => h.CreatedDate))
                                        {
                                            <tr>
                                                <td>@item.Id</td>
                                                <td>@item.CreatedDate.ToShortDateString()</td>
                                                <td>@item.CreatedBy</td>
                                                <td>@item.Status</td>
                                                <td>@(item.ImageType ?? "N/A")</td>
                                                <td>
                                                    <HxButton Text="View" Color="ThemeColor.Secondary" Size="ButtonSize.Small" OnClick="@(async () => await OpenViewModal(item))" CssClass="me-2" />
                                                    @if (item.Status == "Submitted")
                                                    {
                                                        <HxButton Text="Approve" Color="ThemeColor.Success" Size="ButtonSize.Small" OnClick="@(async () => await OpenApproveModal(item))" />
                                                    }
                                                </td>
                                            </tr>
                                        }
                                    </tbody>
                                </table>
                            </div>
                        }
                        else
                        {
                            <div class="alert alert-info">
                                No history records available. Upload and submit images to see them in the history.
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>
    }
    else
    {
        <div class="row">
            <div class="col-12">
                <div class="alert alert-info">
                    @if (!isPracticeManager && !isMarketingTeamMember)
                    {
                        <p>You don't have permission to edit or approve images. Only Practice Managers and Marketing Team members can perform these actions.</p>
                    }
                    else if (!(isPracticeManager && isMarketingTeamMember))
                    {
                        <p>You need to be both a Practice Manager and a Marketing Team member to switch between views.</p>
                    }
                    else
                    {
                        <p>Select a view type above to see the history table.</p>
                    }
                </div>
            </div>
        </div>
    }


    <HxModal @ref="uploadModal" Title="Upload Profile Image">
        <BodyTemplate>
            <div class="mb-3">
                <HxInputFile Accept="image/*" OnChange="HandleImageSelected" />
                @if (selectedImagePreview != null)
                {
                    <div class="image-preview-container">
                        <div class="position-relative w-100 text-center">
                            <img src="@selectedImagePreview" alt="Preview" class="image-preview rounded shadow-sm" />
                            <button type="button" class="btn-close position-absolute top-0 end-0 bg-light rounded-circle m-1"
                            @onclick="ClearSelectedImage" aria-label="Remove selected image"></button>
                        </div>
                    </div>
                }
            </div>
        </BodyTemplate>
        <FooterTemplate>
            <HxButton Text="Upload" Color="ThemeColor.Primary" OnClick="@(async () => await UploadImage())" Enabled="@(selectedImage != null)" />
            <HxButton Text="Cancel" Color="ThemeColor.Secondary" OnClick="@(async () => await CloseUploadModal())" />
        </FooterTemplate>
    </HxModal>


    <HxModal @ref="viewModal" Title="View Image">
        <BodyTemplate>
            <div class="text-center">
                @if (selectedHistoryItem != null && !string.IsNullOrEmpty(selectedHistoryItem.ImageUrl))
                {
                    <div class="mb-3">
                        <p><strong>Image URL Type:</strong> @(selectedHistoryItem.ImageUrl.StartsWith("data:image") ? "Data URL" : "HTTP URL")</p>
                    </div>

                    <img src="@selectedHistoryItem.ImageUrl" alt="History Image" class="img-fluid rounded profile-image" />

                    <div class="mt-3">
                        <h6>Image Details:</h6>

                        <p><strong>Status:</strong> @selectedHistoryItem.Status</p>
                        <p><strong>Image Type:</strong> @(selectedHistoryItem.ImageType ?? "N/A")</p>
                        <p><strong>Created By:</strong> @selectedHistoryItem.CreatedBy</p>
                        <p><strong>Created Date:</strong> @selectedHistoryItem.CreatedDate.ToString("yyyy-MM-dd HH:mm:ss")</p>

                        @if (!string.IsNullOrEmpty(selectedHistoryItem.Notes))
                        {
                            <p><strong>Notes:</strong> @selectedHistoryItem.Notes</p>
                        }
                    </div>
                }
                else
                {
                    <div class="image-placeholder">
                        <span>Image not available</span>
                    </div>
                }
            </div>
        </BodyTemplate>
        <FooterTemplate>

            <HxButton Text="Download" Color="ThemeColor.Primary" OnClick="@(async () => await DownloadImage())" Enabled="@(selectedHistoryItem != null && !string.IsNullOrEmpty(selectedHistoryItem.ImageUrl))" />
            <HxButton Text="Close" Color="ThemeColor.Secondary" OnClick="@(async () => await CloseViewModal())" />
        </FooterTemplate>
    </HxModal>


    <HxModal @ref="approveModal" Title="Approve Image">
        <BodyTemplate>
            <div class="mb-4">
                <h5 class="mb-3">Original Image</h5>
                <div class="text-center">
                    @if (selectedHistoryItem != null && !string.IsNullOrEmpty(selectedHistoryItem.ImageUrl))
                    {
                        <div class="mb-3">
                            <p><strong>Image URL Type:</strong> @(selectedHistoryItem.ImageUrl.StartsWith("data:image") ? "Data URL" : "HTTP URL")</p>
                        </div>

                        <img src="@selectedHistoryItem.ImageUrl" alt="Original Image" class="img-fluid rounded mb-3 profile-image" />
                    }
                    else
                    {
                        <div class="image-placeholder mb-3">
                            <span>Image not available</span>
                        </div>
                    }
                </div>
            </div>

            <hr class="my-4" />

            <div class="mb-4">
                <h5 class="mb-3">Branded Image</h5>
                <div class="mb-3">
                    <HxInputFile Accept="image/*" OnChange="HandleBrandedImageSelected" />
                </div>
                <div class="text-center">
                    @if (brandedImagePreview != null)
                    {
                        <div class="position-relative">
                            <img src="@brandedImagePreview" alt="Branded Image Preview" class="img-fluid rounded mb-3 profile-image" />
                            <button type="button" class="btn-close position-absolute top-0 end-0 bg-light rounded-circle m-1"
                            @onclick="ClearBrandedImage" aria-label="Remove branded image"></button>
                        </div>
                    }
                    else
                    {
                        <div class="image-placeholder mb-3">
                            <span>Upload branded image</span>
                        </div>
                    }
                </div>
            </div>

            <div class="mb-3">
                <label class="form-label">Select Image Type</label>
                <select class="form-select" @bind="selectedImageType">
                    <option value="">Please select an image type</option>
                    @if (imageTypes.Any())
                    {
                        @foreach (var imageType in imageTypes)
                        {
                            <option value="@imageType.Code">@imageType.Name</option>
                        }
                    }
                    else
                    {
                        <option value="Thumbnail">Thumbnail</option>
                        <option value="Photo">Photo</option>
                    }
                </select>
            </div>
        </BodyTemplate>
        <FooterTemplate>
            <HxButton Text="Confirm Approval" Color="ThemeColor.Success" OnClick="@(async () => await ApproveImage())" Enabled="@(brandedImagePreview != null && !string.IsNullOrEmpty(selectedImageType))" />
            <HxButton Text="Reject" Color="ThemeColor.Danger" OnClick="@(async () => await RejectImage())" />
            <HxButton Text="Cancel" Color="ThemeColor.Secondary" OnClick="@(async () => await CloseApproveModal())" />
        </FooterTemplate>
    </HxModal>


    <HxModal @ref="successModal" Title="Success">
        <BodyTemplate>
            <div class="text-center py-4">
                <div class="mb-4">
                    <HxIcon Icon="@successIcon" CssClass="@(successIcon == BootstrapIcon.CheckCircleFill ? "text-success" : "text-info")" Style="font-size: 4rem;" />
                </div>
                <h4 class="mb-3">@successMessage</h4>
            </div>
        </BodyTemplate>
        <FooterTemplate>
            <HxButton Text="Dismiss" Color="ThemeColor.Primary" OnClick="@(async () => await CloseSuccessModal())" />
        </FooterTemplate>
    </HxModal>


    <HxModal @ref="rejectModal" Title="Reject Image">
        <BodyTemplate>
            <div class="mb-4">
                <p>Please provide a reason for rejecting this image:</p>
                <textarea class="form-control" rows="3" @bind="rejectionReason" placeholder="Enter rejection reason..."></textarea>
            </div>
        </BodyTemplate>
        <FooterTemplate>
            <HxButton Text="Confirm Rejection" Color="ThemeColor.Danger" OnClick="@(async () => await ConfirmRejectImage())" Enabled="@(!string.IsNullOrWhiteSpace(rejectionReason))" />
            <HxButton Text="Cancel" Color="ThemeColor.Secondary" OnClick="@(async () => await CloseRejectModal())" />
        </FooterTemplate>
    </HxModal>
</div>

@code {
    [Parameter]
    public int PracticeId { get; set; }

    [Parameter]
    public string PersonId { get; set; } = string.Empty;

    private string UserRole { get; set; } = "";
    private string CurrentImage { get; set; } = string.Empty;
    private List<ImageHistoryItem> ImageHistory { get; set; } = new();
    private bool isImageUploaded { get; set; } = false;
    private int? uploadedImageId { get; set; } = null;
    private bool isLoadingImages { get; set; } = false;
    private List<ProfileImageInfo> availableImages { get; set; } = new();
    private ProfileImageInfo? currentProfileImage { get; set; }

    private bool isPracticeManager { get; set; } = false;
    private bool isMarketingTeamMember { get; set; } = false;
    private int CurrentUserPracticeId { get; set; } = 0;

    private HxModal uploadModal = null!;
    private HxModal viewModal = null!;
    private HxModal approveModal = null!;
    private HxModal successModal = null!;
    private HxModal rejectModal = null!;

    private IBrowserFile? selectedImage;
    private string? selectedImagePreview;
    private IBrowserFile? brandedImage;
    private string? brandedImagePreview;

    private ImageHistoryItem? selectedHistoryItem;
    private string selectedImageType = string.Empty;
    private List<PublicProfileImageTypeModel> imageTypes = new();

    private string successMessage = string.Empty;
    private BootstrapIcon successIcon = BootstrapIcon.CheckCircleFill;
    private string rejectionReason = string.Empty;

    private void BackToPractice()
    {
        Navigation.NavigateTo($"practice/{PracticeId}");
    }

    private readonly List<PublicProfileImageTypeModel> defaultImageTypes = new()
    {
        new PublicProfileImageTypeModel { Name = "Thumbnail", Code = "Thumbnail" },
        new PublicProfileImageTypeModel { Name = "Photo", Code = "Photo" }
    };

    private HttpClient CreateAuthenticatedClient()
    {
        var httpClient = ClientFactory.CreateClient(ApiSettings.Api);
        httpClient.DefaultRequestHeaders.Add("X-ORIG-NAME", userName);
        return httpClient;
    }

    private void ResetImageState()
    {
        availableImages = new List<ProfileImageInfo>();
        currentProfileImage = null;
        CurrentImage = string.Empty;
        ImageHistory = new List<ImageHistoryItem>();
    }

    private async Task<T?> HandleApiResponse<T>(HttpResponseMessage response, string errorPrefix) where T : class
    {
        if (response.IsSuccessStatusCode)
        {
            return await response.Content.ReadFromJsonAsync<T>();
        }
        else
        {
            var errorContent = await response.Content.ReadAsStringAsync();
            ShowWarningMessage($"{errorPrefix}: {response.StatusCode}");
            return null;
        }
    }



    [CascadingParameter]
    private Task<AuthenticationState> authenticationStateTask { get; set; } = default!;
    private string userName = string.Empty;

    protected override async Task OnInitializedAsync()
    {
        var authState = await authenticationStateTask;
        userName = authState.GetWindowsUserName(UserContext.OverrideUsername);

        isMarketingTeamMember = authState.CheckCurrentUserIsInMarketingTeam();

        if (string.IsNullOrWhiteSpace(UserContext.OverrideUsername))
        {
            var storedOverride = await JSRuntime.InvokeAsync<string>("localStorage.getItem", "overrideUsername");
            if (!string.IsNullOrWhiteSpace(storedOverride))
            {
                UserContext.OverrideUsername = storedOverride;
            }
        }

        CurrentUserPracticeId = authState.GetPracticeId(ClientFactory, UserContext.OverrideUsername);

        isPracticeManager = await GetCurrentPMAsync(PracticeId, CurrentUserPracticeId);

        if (isPracticeManager && isMarketingTeamMember)
        {
            UserRole = "Marketing";
        }
        else if (isMarketingTeamMember)
        {
            UserRole = "Marketing";
        }
        else
        {
            UserRole = "PM";
        }

        await LoadImageTypes();

        await LoadPracticeImages();
    }

    private async Task<bool> GetCurrentPMAsync(int practiceIdFromUrl, int currentUserPracticeId)
    {

        if (practiceIdFromUrl != 0 && currentUserPracticeId != 0)
        {
            return practiceIdFromUrl == currentUserPracticeId;
        }

        await Task.CompletedTask;
        return false;
    }

    private async Task LoadImageTypes()
    {
        try
        {
            var httpClient = CreateAuthenticatedClient();

            var response = await httpClient.GetAsync("publicprofileimagetype");

            var result = await HandleApiResponse<PublicProfileImageTypeResponse>(response, "Error loading image types");

            if (result != null && result.Records.Any())
            {
                imageTypes = result.Records;
                selectedImageType = "";
            }
            else
            {
                imageTypes = defaultImageTypes;
                selectedImageType = "";
            }
        }
        catch
        {
            imageTypes = defaultImageTypes;
            selectedImageType = "";
        }
    }

    private async Task LoadPracticeImages()
    {
        try
        {
            isLoadingImages = true;
            ShowWarningMessage("Loading images, please wait...");

            var httpClient = CreateAuthenticatedClient();
            var simpleJson = $"{{\"personId\":\"{PersonId}\",\"approvalStatus\":[]}}";
            var content = new StringContent(simpleJson, Encoding.UTF8, "application/json");


            var response = await httpClient.PostAsync("publicprofileimages/search", content);

            var result = await HandleApiResponse<PublicProfileImageSearchResponse>(response, "Error loading clinician data");

            if (result != null)
            {
                if (result.Records.Any())
                {
                    var imagePaths = result.Records.Select(img => img.ImageUrl).ToList();
                    var imageDataUrls = await FileStorageHubService.DownloadImagesAsync(imagePaths);

                    availableImages = result.Records.Select(img => new ProfileImageInfo(
                        img.Id,
                        imageDataUrls.TryGetValue(img.ImageUrl, out var dataUrl) ? dataUrl : GetFileStorageHubUrl(img.ImageUrl),
                        imageDataUrls.TryGetValue(img.ImageUrl, out var fullDataUrl) ? fullDataUrl : GetFileStorageHubUrl(img.ImageUrl),
                        $"Image {img.Id}",
                        img.ImageType
                    )).ToList();

                    if (availableImages.Any())
                    {
                        currentProfileImage = null;

                        // Find the published image if it exists
                        var publishedImage = result.Records.FirstOrDefault(img => img.ApprovalStatusId == 5 || img.ApprovalStatus == "Published");

                        if (publishedImage != null)
                        {
                            // Find the corresponding ProfileImageInfo in availableImages
                            currentProfileImage = availableImages.FirstOrDefault(img => img.Id == publishedImage.Id) ?? availableImages.First();
                        }
                        else
                        {
                            // If no published image, use the first one
                            currentProfileImage = availableImages.First();
                            Console.WriteLine("No published image found, using first image as default");
                        }

                        CurrentImage = currentProfileImage.FullImageUrl;
                    }
                }
                else
                {

                    ResetImageState();
                }

                await LoadHistoryData(result.Records);
            }
            else
            {
                ResetImageState();
            }

            ClearLoadingMessage();
        }
        catch (Exception ex)
        {

            ClearLoadingMessage();
            ResetImageState();
            ShowWarningMessage($"Error loading images: {ex.Message}");
        }
    }

    private async Task LoadHistoryData(List<PublicProfileImageModel>? images)
    {
        ImageHistory = new List<ImageHistoryItem>();

        if (images != null && images.Any())
        {
            var imagePaths = images.Select(img => img.ImageUrl).ToList();
            var imageDataUrls = await FileStorageHubService.DownloadImagesAsync(imagePaths);

            ImageHistory = images.Select(img =>
            {
                string imageUrl;
                if (imageDataUrls.TryGetValue(img.ImageUrl, out var dataUrl) && !string.IsNullOrEmpty(dataUrl))
                {
                    imageUrl = dataUrl;
                    Console.WriteLine($"Using data URL for image ID {img.Id}");
                }
                else
                {
                    imageUrl = GetFileStorageHubUrl(img.ImageUrl);
                    Console.WriteLine($"Using constructed URL for image ID {img.Id}: {imageUrl}");
                }

                string status = !string.IsNullOrEmpty(img.ApprovalStatus) ? img.ApprovalStatus : GetStatusText(img.ApprovalStatusId);

                // For submitted images, always set ImageType to null
                string? imageType = (status == "Submitted") ? null : img.ImageType;

                return new ImageHistoryItem
                {
                    Id = img.Id,
                    CreatedDate = img.CreatedDate,
                    CreatedBy = img.CreatedBy,
                    Status = status,
                    ImageUrl = imageUrl,
                    OriginalImageUrl = img.ImageUrl,
                    ImageType = imageType,
                    Notes = img.Notes
                };
            }).ToList();

            Console.WriteLine($"Loaded {ImageHistory.Count} history items");
        }
        else
        {
            Console.WriteLine("No history items found");
        }

        await Task.CompletedTask;
    }

    private string GetStatusText(int approvalStatusId)
    {
        return approvalStatusId switch
        {
            1 => "Submitted",
            2 => "Approved",
            3 => "Rejected",
            4 => "Historic",
            5 => "Published",
            _ => "Unknown"
        };
    }

    // Helper method to create a FileStorageHub URL
    private string GetFileStorageHubUrl(string imagePath)
    {
        if (string.IsNullOrEmpty(imagePath))
        {
            Console.WriteLine("GetFileStorageHubUrl - Empty image path");
            return string.Empty;
        }

        Console.WriteLine($"GetFileStorageHubUrl - Original path: {imagePath}");

        if (imagePath.StartsWith("http"))
        {
            Console.WriteLine($"GetFileStorageHubUrl - Already a full URL: {imagePath}");
            return imagePath;
        }

        // If the URL already includes the download endpoint, just add the base URL
        if (imagePath.StartsWith("/Image/download/"))
        {
            var fullUrl = $"https://apinew.mydentist.co.uk/FileStorageHub.Api.V2/api{imagePath}";
            Console.WriteLine($"GetFileStorageHubUrl - Added base URL to download path: {fullUrl}");
            return fullUrl;
        }

        // If the image path is a base64 string, return it as is
        if (imagePath.StartsWith("data:image"))
        {
            Console.WriteLine($"GetFileStorageHubUrl - Already a data URL, returning as is");
            return imagePath;
        }

        // Otherwise, construct the full URL
        var constructedUrl = $"https://apinew.mydentist.co.uk/FileStorageHub.Api.V2/api/Image/download/{imagePath}";
        Console.WriteLine($"GetFileStorageHubUrl - Constructed full URL: {constructedUrl}");
        return constructedUrl;
    }



    private async Task OpenUploadModal()
    {
        if (!isPracticeManager)
        {
            ShowWarningMessage("Only Practice Managers can upload images.");
            return;
        }

        selectedImage = null;
        selectedImagePreview = null;
        await uploadModal.ShowAsync();
    }

    private async Task CloseUploadModal()
    {
        await uploadModal.HideAsync();
    }

    private async Task OpenViewModal(ImageHistoryItem item)
    {
        selectedHistoryItem = item;

        await viewModal.ShowAsync();
    }

    private async Task CloseViewModal()
    {
        await viewModal.HideAsync();
    }

    private async Task OpenApproveModal(ImageHistoryItem item)
    {
        if (!isMarketingTeamMember)
        {
            ShowWarningMessage("Only Marketing Team members can approve images.");
            return;
        }

        selectedHistoryItem = item;

        Console.WriteLine($"Opening approve modal for image with ID: {item.Id}, Status: {item.Status}, URL: {item.ImageUrl}");

        ClearBrandedImage();
        await approveModal.ShowAsync();
    }

    private async Task CloseApproveModal()
    {
        await approveModal.HideAsync();
    }

    private async Task CloseRejectModal()
    {
        await rejectModal.HideAsync();
    }

    private async Task CloseSuccessModal()
    {
        await successModal.HideAsync();
    }

    private async Task ShowSuccessModal(string message)
    {
        successMessage = message;
        successIcon = BootstrapIcon.CheckCircleFill;
        await successModal.ShowAsync();
    }

    private async Task ShowSuccessModalWithIcon(string message, BootstrapIcon icon)
    {
        successMessage = message;
        successIcon = icon;
        await successModal.ShowAsync();
    }

    private void ShowWarningMessage(string message)
    {
        HxMessengerProvider.AddWarning(message);
    }

    private void ClearLoadingMessage()
    {
        if (isLoadingImages)
        {
            HxMessengerProvider.Clear();
            isLoadingImages = false;
        }
    }

    private async Task HandleImageSelected(InputFileChangeEventArgs e)
    {
        selectedImage = e.File;
        if (selectedImage != null)
        {
            try
            {
                var format = selectedImage.ContentType;

                var maxAllowedSize = 10 * 1024 * 1024; // 10MB max

                using var imageStream = selectedImage.OpenReadStream(maxAllowedSize);
                using var memoryStream = new MemoryStream();
                await imageStream.CopyToAsync(memoryStream);
                var buffer = memoryStream.ToArray();

                selectedImagePreview = $"data:{format};base64,{Convert.ToBase64String(buffer)}";

                Console.WriteLine($"Image loaded: {selectedImage.Name}, Size: {selectedImage.Size / 1024} KB, Type: {format}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error processing image: {ex.Message}");
                selectedImage = null;
                selectedImagePreview = null;
            }
        }
    }

    private void ClearSelectedImage()
    {
        selectedImage = null;
        selectedImagePreview = null;
    }

    private void ClearBrandedImage()
    {
        brandedImage = null;
        brandedImagePreview = null;
    }

    private async Task HandleBrandedImageSelected(InputFileChangeEventArgs e)
    {
        brandedImage = e.File;

        if (brandedImage != null)
        {
            try
            {

                var format = brandedImage.ContentType;

                var maxAllowedSize = 10 * 1024 * 1024; // 10MB max

                using var imageStream = brandedImage.OpenReadStream(maxAllowedSize);
                using var memoryStream = new MemoryStream();
                await imageStream.CopyToAsync(memoryStream);
                var buffer = memoryStream.ToArray();

                brandedImagePreview = $"data:{format};base64,{Convert.ToBase64String(buffer)}";

                Console.WriteLine($"Branded image loaded: {brandedImage.Name}, Size: {brandedImage.Size / 1024} KB, Type: {format}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error processing branded image: {ex.Message}");
                brandedImage = null;
                brandedImagePreview = null;
            }
        }
    }

    private async Task UploadImage()
    {
        if (selectedImage == null || selectedImagePreview == null) return;

        try
        {
            CurrentImage = selectedImagePreview;

            var newImageId = availableImages.Count > 0 ? availableImages.Max(i => i.Id) + 1 : 1;

            var newProfileImage = new ProfileImageInfo(
                newImageId,
                selectedImagePreview,
                selectedImagePreview,
                $"Uploaded Image {newImageId}"
            );

            currentProfileImage = null;

            availableImages.Add(newProfileImage);

            currentProfileImage = newProfileImage;
            isImageUploaded = true;

            uploadedImageId = newProfileImage.Id;

            await CloseUploadModal();
            await ShowSuccessModal("Image selected successfully! Click 'Submit for Approval' to upload.");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error processing image: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
            ShowWarningMessage($"Error processing image: {ex.Message}");
        }
    }
    private async Task DownloadImage()
    {
        if (selectedHistoryItem != null && !string.IsNullOrEmpty(selectedHistoryItem.ImageUrl))
        {
            try
            {
                string fileName = $"clinician_image_{selectedHistoryItem.Id}_{DateTime.Now:yyyyMMdd}.jpg";
                await JSRuntime.InvokeVoidAsync("downloadImage", selectedHistoryItem.ImageUrl, fileName);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error downloading image: {ex.Message}");
                ShowWarningMessage($"Error downloading image: {ex.Message}");
            }
        }
    }



    private async Task RejectImage()
    {
        if (!isMarketingTeamMember)
        {
            ShowWarningMessage("Only Marketing Team members can reject images.");
            return;
        }

        if (selectedHistoryItem != null)
        {
            Console.WriteLine($"Opening reject modal for image with ID: {selectedHistoryItem.Id}, Status: {selectedHistoryItem.Status}, URL: {selectedHistoryItem.ImageUrl}");
        }

        rejectionReason = string.Empty;
        await rejectModal.ShowAsync();
    }

    private async Task ConfirmRejectImage()
    {
        if (!isMarketingTeamMember)
        {
            ShowWarningMessage("Only Marketing Team members can reject images.");
            return;
        }

        if (selectedHistoryItem != null && !string.IsNullOrWhiteSpace(rejectionReason))
        {
            try
            {
                Console.WriteLine($"Rejecting image with ID: {selectedHistoryItem.Id}, Status: {selectedHistoryItem.Status}, URL: {selectedHistoryItem.ImageUrl}");
                ShowWarningMessage("Processing rejection, please wait...");
                selectedHistoryItem.Status = "Rejected";

                var httpClient = CreateAuthenticatedClient();
                string originalImageUrl = selectedHistoryItem.OriginalImageUrl;
                Console.WriteLine($"Using original ImageUrl from history item: {originalImageUrl}");
                var practice = PracticeContext.PracticeId;
                if (practice == 0) { practice = await getPracticeIdFromLocalStorage(); }
                var rejectData = new
                {
                    id = selectedHistoryItem.Id,
                    personId = PersonId,
                    imageUrl = originalImageUrl,
                    approvalStatusId = 3,
                    notes = $"Rejected: {rejectionReason}",
                    createdBy = selectedHistoryItem.CreatedBy,
                    createdDate = selectedHistoryItem.CreatedDate.ToString("yyyy-MM-ddTHH:mm:ss.fff"),
                    updatedBy = userName,
                    updatedDate = DateTime.Now.ToString("yyyy-MM-ddTHH:mm:ss.fff"),
                    practice = practice
                };

                string rejectJson = System.Text.Json.JsonSerializer.Serialize(rejectData);
                var content = new StringContent(rejectJson, Encoding.UTF8, "application/json");
                Console.WriteLine($"PUT URL: publicprofileimages/{selectedHistoryItem.Id}");
                var response = await httpClient.PutAsync($"publicprofileimages/{selectedHistoryItem.Id}", content);

                if (response.IsSuccessStatusCode)
                {
                    await CloseRejectModal();
                    await CloseApproveModal();
                    await ShowSuccessModalWithIcon($"Image has been rejected. Reason: {rejectionReason}", BootstrapIcon.XCircleFill);
                    await LoadPracticeImages();
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    ShowWarningMessage($"Error updating image status: {response.StatusCode} - {errorContent}");
                }
            }
            catch (Exception ex)
            {
                ShowWarningMessage($"An error occurred while rejecting the image: {ex.Message}");
            }
        }
        else if (string.IsNullOrWhiteSpace(rejectionReason))
        {
            ShowWarningMessage("Please provide a reason for rejecting the image.");
        }
    }

    private async Task ApproveImage()
    {
        if (!isMarketingTeamMember)
        {
            ShowWarningMessage("Only Marketing Team members can approve images.");
            return;
        }

        if (string.IsNullOrEmpty(selectedImageType))
        {
            ShowWarningMessage("Please select an image type before approving.");
            return;
        }

        if (selectedHistoryItem != null && brandedImagePreview != null)
        {
            try
            {
                ShowWarningMessage("Processing approval and uploading branded image, please wait...");
                selectedHistoryItem.Status = "Approved";
                var httpClient = CreateAuthenticatedClient();
                string originalImageUrl = selectedHistoryItem.OriginalImageUrl;
                int recordId = selectedHistoryItem.Id;


                var practice = PracticeContext.PracticeId;
                if (practice == 0) { practice = await getPracticeIdFromLocalStorage(); }
                var approveData = new
                {
                    id = recordId,
                    personId = PersonId,
                    imageUrl = originalImageUrl,
                    approvalStatusId = 2,
                    notes = "Approved for branding",
                    createdBy = selectedHistoryItem.CreatedBy,
                    createdDate = selectedHistoryItem.CreatedDate.ToString("yyyy-MM-ddTHH:mm:ss.fff"),
                    updatedBy = userName,
                    updatedDate = DateTime.Now.ToString("yyyy-MM-ddTHH:mm:ss.fff"),
                    practice = practice
                };

                // Serialize the object to JSON using System.Text.Json
                string approveJson = System.Text.Json.JsonSerializer.Serialize(approveData);
                var approveContent = new StringContent(approveJson, Encoding.UTF8, "application/json");

                // Update the original image status in the API using PUT request to the specific record
                var approveResponse = await httpClient.PutAsync($"publicprofileimages/{recordId}", approveContent);

                if (!approveResponse.IsSuccessStatusCode)
                {
                    var errorContent = await approveResponse.Content.ReadAsStringAsync();
                    ShowWarningMessage($"Error updating original image status: {approveResponse.StatusCode} - {errorContent}");
                    return;
                }

                // Now, upload the branded image to FileStorageHub
                string base64Data = brandedImagePreview;

                // Get the file extension and name from the branded image if available
                string extension = ".jpg"; // Default extension
                string fileName = "branded_image"; // Default file name

                if (brandedImage != null)
                {
                    // Get the extension
                    var extMatch = Regex.Match(brandedImage.Name, @"\.[^.]+$");
                    extension = extMatch.Success ? extMatch.Value : ".jpg";

                    fileName = Path.GetFileNameWithoutExtension(brandedImage.Name);

                    fileName = Regex.Replace(fileName, @"[^\w\-\.]", "_");

                    // If the file name is empty, use a default
                    if (string.IsNullOrWhiteSpace(fileName))
                    {
                        fileName = "branded_image";
                    }

                }

                // Generate a personal number and full name for the image path
                string personalNumber = PersonId;
                string fullName = $"Person{PersonId}";

                // Add the file name to the base64 data to help the FileStorageHubService extract it
                if (!base64Data.Contains("name="))
                {
                    // Add the file name to the base64 data
                    int index = base64Data.IndexOf(";base64,");
                    if (index > 0)
                    {
                        base64Data = base64Data.Insert(index, $";name=branded_{fileName}{extension}");
                    }
                }

                // Upload the branded image to the FileStorageHub
                string imagePath = await FileStorageHubService.UploadImageAsync(
                    base64Data,
                    personalNumber,
                    fullName
                );

                if (imagePath.StartsWith("data:image"))
                {
                    // This should not happen, but just in case
                    ShowWarningMessage("Error: Cannot save data URL to database. Please try again.");
                    return;
                }

                // Before setting the new image to Published, check if there are any existing Published images
                // and change their status to Historic
                var searchJson = $"{{\"personId\":\"{PersonId}\",\"approvalStatus\":[\"Published\"]}}";
                var searchContent = new StringContent(searchJson, Encoding.UTF8, "application/json");
                var searchResponse = await httpClient.PostAsync("publicprofileimages/search", searchContent);

                if (searchResponse.IsSuccessStatusCode)
                {
                    var publishedImages = await searchResponse.Content.ReadFromJsonAsync<PublicProfileImageSearchResponse>();

                    if (publishedImages != null && publishedImages.Records.Any())
                    {
                        Console.WriteLine($"Found {publishedImages.Records.Count} published images that will be changed to Historic");

                        // Update each published image to Historic status
                        foreach (var publishedImage in publishedImages.Records)
                        {
                            var historicData = new
                            {
                                id = publishedImage.Id,
                                personId = PersonId,
                                imageUrl = publishedImage.ImageUrl,
                                approvalStatusId = 4, // Historic status
                                notes = "Changed to Historic as a new image was published",
                                createdBy = publishedImage.CreatedBy,
                                createdDate = publishedImage.CreatedDate.ToString("yyyy-MM-ddTHH:mm:ss.fff"),
                                updatedBy = userName,
                                updatedDate = DateTime.Now.ToString("yyyy-MM-ddTHH:mm:ss.fff"),
                                practice = practice,
                                imageType = publishedImage.ImageType
                            };

                            string historicJson = System.Text.Json.JsonSerializer.Serialize(historicData);
                            var historicContent = new StringContent(historicJson, Encoding.UTF8, "application/json");

                            var historicResponse = await httpClient.PutAsync($"publicprofileimages/{publishedImage.Id}", historicContent);

                            if (historicResponse.IsSuccessStatusCode)
                            {
                                // Update the record in the local ImageHistory collection
                                var historyItem = ImageHistory.FirstOrDefault(h => h.Id == publishedImage.Id);
                                if (historyItem != null)
                                {
                                    historyItem.Status = "Historic";
                                    Console.WriteLine($"Updated local history item {publishedImage.Id} to Historic status");
                                }
                            }
                            else
                            {
                                var errorContent = await historicResponse.Content.ReadAsStringAsync();
                                Console.WriteLine($"Error updating image {publishedImage.Id} to Historic: {historicResponse.StatusCode} - {errorContent}");
                            }
                        }
                    }
                }

                // Create an object for serialization instead of manually constructing JSON
                var brandedImageData = new
                {
                    personId = PersonId,
                    imageUrl = imagePath,
                    approvalStatusId = 5,
                    approvalStatus = "Published",
                    imageType = selectedImageType,
                    notes = "Branded image approved and published",
                    createdBy = userName,
                    createdDate = DateTime.Now.ToString("yyyy-MM-ddTHH:mm:ss.fff"),
                    practice = practice
                };


                string simpleJson = System.Text.Json.JsonSerializer.Serialize(brandedImageData);
                var content = new StringContent(simpleJson, Encoding.UTF8, "application/json");


                var response = await httpClient.PostAsync("publicprofileimages", content);

                if (response.IsSuccessStatusCode)
                {

                    var createdImage = await response.Content.ReadFromJsonAsync<PublicProfileImageModel>();

                    if (createdImage != null)
                    {

                        var newImageId = availableImages.Count > 0 ? availableImages.Max(i => i.Id) + 1 : 1;
                        var imageUrl = GetFileStorageHubUrl(imagePath);
                        var newProfileImage = new ProfileImageInfo(
                            newImageId,
                            imageUrl,
                            imageUrl,
                            $"Branded {selectedImageType} Image",
                            selectedImageType
                        );

                        currentProfileImage = null;
                        availableImages.Add(newProfileImage);
                        currentProfileImage = newProfileImage;
                        CurrentImage = newProfileImage.FullImageUrl;
                        var newHistoryItem = new ImageHistoryItem
                        {
                            Id = createdImage.Id,
                            CreatedDate = createdImage.CreatedDate,
                            CreatedBy = createdImage.CreatedBy,
                            Status = "Published",
                            ImageUrl = GetFileStorageHubUrl(imagePath),
                            OriginalImageUrl = imagePath,
                            ImageType = createdImage.ImageType ?? selectedImageType,
                            Notes = createdImage.Notes
                        };

                        ImageHistory.Add(newHistoryItem);

                        await CloseApproveModal();

                        // Check if we found and updated any previously published images
                        var publishedImages = await searchResponse.Content.ReadFromJsonAsync<PublicProfileImageSearchResponse>();
                        if (publishedImages != null && publishedImages.Records.Any())
                        {
                            await ShowSuccessModal($"Original image has been approved and branded {selectedImageType} image has been published. {publishedImages.Records.Count} previously published image(s) changed to Historic status.");
                        }
                        else
                        {
                            await ShowSuccessModal($"Original image has been approved and branded {selectedImageType} image has been published to the carousel.");
                        }

                        await LoadPracticeImages();
                    }
                    else
                    {
                        ShowWarningMessage("Error submitting branded image: Unable to process server response.");
                    }
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    ShowWarningMessage($"Error submitting branded image: {response.StatusCode} - {errorContent}");
                }
            }
            catch (Exception ex)
            {
                ShowWarningMessage($"An error occurred while approving the image: {ex.Message}");
            }
        }
        else if (brandedImagePreview == null)
        {
            ShowWarningMessage("Please upload a branded image before approving.");
        }
    }

    private async Task RemoveCurrentImage()
    {
        if (currentProfileImage != null && uploadedImageId.HasValue)
        {
            try
            {
                var imageToRemove = availableImages.FirstOrDefault(img => img.Id == uploadedImageId.Value);
                if (imageToRemove != null)
                {
                    availableImages.Remove(imageToRemove);
                }

                CurrentImage = string.Empty;
                currentProfileImage = null;
                isImageUploaded = false;
                uploadedImageId = null;
                await ShowSuccessModalWithIcon("The image has been removed successfully.", BootstrapIcon.TrashFill);
            }
            catch (Exception ex)
            {
                ShowWarningMessage($"Error removing image: {ex.Message}");
            }
        }
    }

    private async Task SubmitForApproval()
    {
        if (!isPracticeManager)
        {
            ShowWarningMessage("Only Practice Managers can submit images for approval.");
            return;
        }

        if (currentProfileImage == null || string.IsNullOrEmpty(CurrentImage) || !isImageUploaded)
        {
            ShowWarningMessage("No image available to submit for approval.");
            return;
        }

        try
        {
            ShowWarningMessage("Uploading image, please wait...");
            string base64Data = CurrentImage;

            string extension = ".jpg";
            string fileName = "image";

            if (selectedImage != null)
            {
                var extMatch = Regex.Match(selectedImage.Name, @"\.[^.]+$");
                extension = extMatch.Success ? extMatch.Value : ".jpg";
                fileName = Path.GetFileNameWithoutExtension(selectedImage.Name);
                fileName = Regex.Replace(fileName, @"[^\w\-\.]", "_");

                if (string.IsNullOrWhiteSpace(fileName))
                {
                    fileName = "image";
                }

            }

            string personalNumber = PersonId;
            string fullName = $"Person{PersonId}";

            if (!base64Data.Contains("name="))
            {
                int index = base64Data.IndexOf(";base64,");
                if (index > 0)
                {
                    base64Data = base64Data.Insert(index, $";name={fileName}{extension}");
                }
            }
            string imagePath = await FileStorageHubService.UploadImageAsync(
                base64Data,
                personalNumber,
                fullName
            );

            var httpClient = CreateAuthenticatedClient();
            var practice = PracticeContext.PracticeId;
            if (practice == 0) { practice = await getPracticeIdFromLocalStorage(); }
            var submitData = new
            {
                personId = PersonId,
                imageUrl = imagePath,
                approvalStatusId = 1,
                approvalStatus = "Submitted",
                notes = "Submitted for approval",
                createdBy = userName,
                createdDate = DateTime.Now.ToString("yyyy-MM-ddTHH:mm:ss.fff"),
                practice = practice
            };

            string simpleJson = System.Text.Json.JsonSerializer.Serialize(submitData);
            var content = new StringContent(simpleJson, Encoding.UTF8, "application/json");
            var response = await httpClient.PostAsync("publicprofileimages", content);

            if (response.IsSuccessStatusCode)
            {

                var createdImage = await response.Content.ReadFromJsonAsync<PublicProfileImageModel>();

                if (createdImage != null)
                {

                    var newHistoryItem = new ImageHistoryItem
                    {
                        Id = createdImage.Id,
                        CreatedDate = createdImage.CreatedDate,
                        CreatedBy = createdImage.CreatedBy,
                        Status = "Submitted",
                        ImageUrl = GetFileStorageHubUrl(createdImage.ImageUrl),
                        OriginalImageUrl = createdImage.ImageUrl,
                        ImageType = null,
                        Notes = createdImage.Notes
                    };

                    ImageHistory.Add(newHistoryItem);

                    isImageUploaded = false;
                    uploadedImageId = null;
                    CurrentImage = string.Empty;
                    currentProfileImage = null;
                    await ShowSuccessModal("Image submitted for approval successfully!");
                    await LoadPracticeImages();
                }
                else
                {
                    ShowWarningMessage("Error submitting image: Unable to process server response.");
                }
            }
            else
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                ShowWarningMessage($"Error submitting image: {response.StatusCode} - {errorContent}");
            }
        }
        catch (Exception ex)
        {
            ShowWarningMessage($"Error submitting image: {ex.Message}");
        }
    }

    private void SelectImageForView(ProfileImageInfo imageToSelect)
    {
        var previousImage = currentProfileImage;

        if (isImageUploaded && uploadedImageId.HasValue && uploadedImageId.Value != imageToSelect.Id)
        {
            ShowWarningMessage("Please submit or remove the current image before selecting another one.");
            return;
        }

        currentProfileImage = imageToSelect;
        CurrentImage = imageToSelect.FullImageUrl;

        if (uploadedImageId.HasValue && uploadedImageId.Value == imageToSelect.Id)
        {
            isImageUploaded = true;
        }
        else
        {
            isImageUploaded = false;
        }


    }


    private class ImageHistoryItem
    {
        public int Id { get; set; }
        public DateTime CreatedDate { get; set; }
        public string CreatedBy { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public string ImageUrl { get; set; } = string.Empty;

        public string OriginalImageUrl { get; set; } = string.Empty;
        public string? ImageType { get; set; }
        public string? Notes { get; set; }
    }


    private class ProfileImageInfo
    {
        public int Id { get; set; }
        public string ThumbnailUrl { get; set; }
        public string FullImageUrl { get; set; }
        public string AltText { get; set; }
        public string? ImageType { get; set; }

        public ProfileImageInfo(int id, string thumbnailUrl, string fullImageUrl, string altText, string? imageType = null)
        {
            Id = id;
            ThumbnailUrl = thumbnailUrl;
            FullImageUrl = fullImageUrl;
            AltText = altText;
            ImageType = imageType;
        }
    }

    private async Task<int> getPracticeIdFromLocalStorage()
    {
        string storedPracticeId = await JSRuntime.InvokeAsync<string>("localStorage.getItem", "practiceId");
        if (!int.TryParse(storedPracticeId, out int practiceId))
            return 0;
        return practiceId;
    }
}
